<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USI AI-OS™ Smart Order v8</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/toast.css?v=8">
    <link rel="stylesheet" href="css/language-switcher.css?v=8">
    <link rel="stylesheet" href="css/fallback-speech.css?v=8">

    <!-- 必要的 JavaScript 文件 -->
    <script src="js/checkout-fix.js?v=8"></script>
    <script src="js/order-confirmation-fix.js?v=8"></script>
    <script src="js/enhanced-gemini-extraction.js?v=8"></script>
    <script src="js/language-resources.js?v=8"></script>
    <script src="js/speech-manager.js?v=9"></script>
    <!-- 暫時停用舊的語音識別，避免衝突 -->
    <!-- <script src="js/speech-recognition.js?v=8"></script> -->
    <script src="js/fallback-speech-ui.js?v=8"></script>
    <style>
        /* ========== 基礎樣式 ========== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --background-color: #F5F5F5;
            --card-background: #FFFFFF;
            --text-primary: #333333;
            --text-secondary: #666666;
            --border-color: #E0E0E0;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.5;
        }

        /* ========== Header 區域 ========== */
        .header {
            height: 60px;
            background-color: var(--card-background);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .brand-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .environment-badge {
            background: var(--warning-color);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .language-switcher {
            position: relative;
        }

        .language-btn {
            background: none;
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 120px;
            display: none;
        }

        .language-option {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
        }

        .language-option:hover {
            background-color: #F5F5F5;
        }

        .language-option:last-child {
            border-bottom: none;
        }

        /* ========== 主要內容區域 ========== */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .hero-title {
            font-size: 32px;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .hero-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 32px;
        }

        /* ========== 輸入區域 ========== */
        .input-section {
            background: var(--card-background);
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 32px;
        }

        .input-label {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            display: block;
        }

        .natural-language-input {
            width: 100%;
            min-height: 120px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .natural-language-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .natural-language-input::placeholder {
            color: #999;
        }

        .input-controls {
            display: flex;
            gap: 16px;
            margin-top: 20px;
            align-items: center;
            justify-content: center;
        }

        .voice-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .voice-btn:hover {
            background-color: #1976D2;
            transform: scale(1.05);
        }

        .voice-btn.recording {
            background-color: var(--error-color);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .primary-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .primary-btn:hover {
            background-color: #1976D2;
            transform: translateY(-2px);
        }

        .primary-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .secondary-btn {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            padding: 10px 30px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .secondary-btn:hover {
            background-color: #E3F2FD;
        }

        /* ========== 語音狀態顯示 ========== */
        .voice-status {
            display: none;
            text-align: center;
            padding: 20px;
            background: #E3F2FD;
            border-radius: 8px;
            margin-top: 16px;
        }

        .voice-status.active {
            display: block;
        }

        .voice-wave {
            display: flex;
            justify-content: center;
            gap: 4px;
            margin: 16px 0;
        }

        .wave-bar {
            width: 4px;
            height: 20px;
            background-color: var(--primary-color);
            border-radius: 2px;
            animation: wave 1s infinite;
        }

        .wave-bar:nth-child(2) { animation-delay: 0.1s; }
        .wave-bar:nth-child(3) { animation-delay: 0.2s; }
        .wave-bar:nth-child(4) { animation-delay: 0.3s; }
        .wave-bar:nth-child(5) { animation-delay: 0.4s; }

        @keyframes wave {
            0%, 100% { height: 20px; }
            50% { height: 40px; }
        }

        /* ========== 結果顯示區域 ========== */
        .results-section {
            margin-bottom: 32px;
            display: none;
        }

        .results-section.show {
            display: block;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .ai-response {
            background: #E8F5E9;
            border-left: 4px solid var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 24px;
        }

        .ai-response-text {
            font-size: 16px;
            line-height: 1.6;
        }

        .menu-items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .menu-item-card {
            background: var(--card-background);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .menu-item-card:hover {
            transform: translateY(-4px);
        }

        /* 尺寸選擇卡片樣式 */
        .size-selection-card {
            border: 2px solid #FFC107;
            background: linear-gradient(135deg, #FFF9C4 0%, #FFFFFF 100%);
        }

        .size-selection-prompt {
            color: #F57C00;
            font-weight: 600;
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }

        .size-selection-controls {
            margin-top: 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .size-option-btn {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: white;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .size-option-btn:hover {
            border-color: var(--primary-color);
            background: #E3F2FD;
            transform: translateX(4px);
        }

        .size-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .size-price {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 16px;
        }

        .item-header {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .item-image {
            width: 60px;
            height: 60px;
            background: #F5F5F5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: var(--text-secondary);
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .item-price {
            font-size: 20px;
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 8px;
        }

        .item-description {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .item-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .quantity-control {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .quantity-btn {
            width: 32px;
            height: 32px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .quantity-display {
            font-size: 16px;
            font-weight: 600;
            min-width: 24px;
            text-align: center;
        }

        .remove-btn {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .remove-btn:hover {
            background: #D32F2F;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 24px;
        }

        /* ========== 訂單摘要 ========== */
        .order-summary {
            background: var(--card-background);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 32px;
            display: none;
        }

        .order-summary.show {
            display: block;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .summary-item:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: var(--secondary-color);
        }

        .estimated-time {
            text-align: center;
            padding: 16px;
            background: #FFF3E0;
            border-radius: 8px;
            margin: 20px 0;
            color: var(--warning-color);
            font-weight: 600;
        }

        /* ========== 載入和通知 ========== */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-overlay.show {
            display: flex;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #E8F5E9;
            color: var(--secondary-color);
            border-left: 4px solid var(--secondary-color);
        }

        .notification.error {
            background: #FFEBEE;
            color: var(--error-color);
            border-left: 4px solid var(--error-color);
        }

        .notification.info {
            background: #E3F2FD;
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }

        /* ========== TTS 測試按鈕 ========== */
        .floating-tts-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-tts-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0,0,0,0.4);
        }

        .floating-tts-btn:active {
            transform: scale(0.95);
        }

        /* ========== 響應式設計 ========== */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .hero-title {
                font-size: 24px;
            }

            .input-section {
                padding: 20px;
            }

            .input-controls {
                flex-direction: column;
                gap: 12px;
            }

            .menu-items-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .floating-tts-btn {
                bottom: 15px;
                right: 15px;
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="brand-title">
            <i class="fas fa-utensils"></i> USI AI-OS™ Smart Order
        </div>
        <div class="language-switcher">
            <button class="language-btn">
                <i class="fas fa-globe"></i>
                <span data-i18n="language_zh"></span>
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="language-dropdown">
                <div class="language-option" data-lang="zh-TW" data-i18n="language_zh"></div>
                <div class="language-option" data-lang="en" data-i18n="language_en">English</div>
                <div class="language-option" data-lang="ja" data-i18n="language_ja"></div>
            </div>
        </div>
    </header>

    <!-- 主要內容 -->
    <div class="container">
        <!-- Hero Section -->
        <section class="hero-section">
            <h1 class="hero-title">
                <i class="fas fa-magic"></i> <span data-i18n="natural_order_title"></span>
            </h1>
            <p class="hero-subtitle">
                <span data-i18n="hero_subtitle"></span>
            </p>
        </section>

        <!-- 輸入區域 -->
        <section class="input-section">
            <label class="input-label">
                <i class="fas fa-comment-dots"></i> <span data-i18n="order_input_label"></span>
            </label>
            
            <textarea 
                class="natural-language-input" 
                id="userInput"
                data-i18n-placeholder="order_input_placeholder"
                placeholder=""
            ></textarea>
            
            <div class="input-controls">
                <button class="voice-btn" id="voiceBtn" data-i18n-title="voice_input_btn" title="">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="primary-btn" id="processBtn">
                    <i class="fas fa-search"></i> <span data-i18n="submit_order_btn"></span>
                </button>
            </div>

            <!-- 語音狀態顯示 -->
            <div class="voice-status" id="voiceStatus">
                <div><i class="fas fa-microphone"></i> <span data-i18n="speech_listening"></span></div>
                <div class="voice-wave">
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                </div>
                <button class="secondary-btn" id="stopVoiceBtn" data-i18n="speech_stopped"></button>
            </div>
        </section>

        <!-- AI 回應區域 -->
        <section class="results-section" id="resultsSection">
            <h2 class="section-title">
                <i class="fas fa-robot"></i> <span data-i18n="gemini_ai_response"></span>
            </h2>
            
            <div class="ai-response" id="aiResponse">
                <div class="ai-response-text" id="aiResponseText">
                    <!-- AI 回應將顯示在這裡 -->
                </div>
            </div>

            <!-- 識別到的餐點 -->
            <h3 class="section-title">
                <i class="fas fa-list-ul"></i> <span data-i18n="recognized_items"></span>
            </h3>
            
            <div class="menu-items-grid" id="menuItemsGrid">
                <!-- 動態生成的餐點卡片將顯示在這裡 -->
            </div>

            <div class="action-buttons">
                <button class="secondary-btn" id="continueOrderBtn">
                    <i class="fas fa-plus"></i> <span data-i18n="continue_order_btn"></span>
                </button>
                <button class="primary-btn" id="confirmOrderBtn">
                    <i class="fas fa-check"></i> <span data-i18n="confirm_order_btn"></span>
                </button>
            </div>
        </section>

        <!-- 訂單摘要 -->
        <section class="order-summary" id="orderSummary">
            <h2 class="section-title">
                <i class="fas fa-receipt"></i> <span data-i18n="order_summary"></span>
            </h2>
            
            <div id="summaryItems">
                <!-- 動態生成的訂單項目 -->
            </div>

            <div class="estimated-time">
                <i class="fas fa-clock"></i> <span data-i18n="estimated_time"></span>
            </div>

            <div class="action-buttons">
                <button class="secondary-btn" id="backToEditBtn">
                    <i class="fas fa-arrow-left"></i> <span data-i18n="back_to_edit_btn"></span>
                </button>
                <button class="primary-btn" id="finalConfirmBtn">
                    <i class="fas fa-credit-card"></i> <span data-i18n="final_confirm_btn"></span>
                </button>
            </div>
        </section>
    </div>

    <!-- 通知訊息 -->
    <div class="notification" id="notification">
        <i class="fas fa-check-circle"></i>
        <span id="notificationText" data-i18n="success_message"></span>
    </div>

    <!-- 載入覆蓋層 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <div id="loadingText" data-i18n="processing_text"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/session-manager.js?v=1"></script>
    <script>
        // 全域變數
        let currentOrder = [];
        let isRecording = false;
        let recognition = null;
        let currentLanguage = 'zh-TW';
        
        // 語音合成功能
        const speechSynthesis = window.speechSynthesis;
        let speechEnabled = true;
        let lastUserInteraction = Date.now();

        // DOM 元素
        const userInput = document.getElementById('userInput');
        const voiceBtn = document.getElementById('voiceBtn');
        const processBtn = document.getElementById('processBtn');
        const voiceStatus = document.getElementById('voiceStatus');
        const stopVoiceBtn = document.getElementById('stopVoiceBtn');
        const resultsSection = document.getElementById('resultsSection');
        const aiResponseText = document.getElementById('aiResponseText');
        const menuItemsGrid = document.getElementById('menuItemsGrid');
        const orderSummary = document.getElementById('orderSummary');
        const summaryItems = document.getElementById('summaryItems');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notificationText');
        const loadingText = document.getElementById('loadingText');

        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            initializeApp();
            setupEventListeners();
            await initializeSpeechRecognition();
            loadMenuData();
        });

        function initializeApp() {
            console.log('GUI 測試環境已初始化');
            
            // 初始化語言設置
            const savedLanguage = localStorage.getItem('preferredLanguage') || 'zh-TW';
            currentLanguage = savedLanguage;
            
            // 等待 language-resources.js 載入後再更新頁面語言
            if (typeof window.updatePageLanguage === 'function') {
                window.updatePageLanguage();
            } else {
                // 如果 language-resources.js 還沒載入，延遲執行
                setTimeout(() => {
                    if (typeof window.updatePageLanguage === 'function') {
                        window.updatePageLanguage();
                    }
                }, 100);
            }
            
            showNotification(getTranslation('welcome_natural_order_system'), 'success');
        }
        
        // ========== 語音合成功能 ==========
        
        // 記錄用戶互動
        function recordUserInteraction() {
            lastUserInteraction = Date.now();
        }
        
        // 檢查語音權限
        function checkSpeechPermission() {
            // 檢查是否在用戶互動後的合理時間內
            const timeSinceInteraction = Date.now() - lastUserInteraction;
            return timeSinceInteraction < 5000; // 5秒內的互動被認為是有效的
        }
        
        // 檢測是否在 Capacitor 環境中
        function isCapacitorEnvironment() {
            try {
                // 檢查 window.Capacitor 是否存在
                if (window.Capacitor && typeof window.Capacitor.isNativePlatform === 'function') {
                    const isNative = window.Capacitor.isNativePlatform();
                    console.log('🔍 Capacitor 環境檢測 (window.Capacitor):', isNative);
                    return isNative;
                }
                console.log('🔍 window.Capacitor 不存在，非 Capacitor 環境');
                return false;
            } catch (error) {
                console.log('🔍 Capacitor 環境檢測失敗:', error);
                return false;
            }
        }

        // 等待 Capacitor 載入的函數
        function waitForCapacitor(maxWaitTime = 5000) {
            return new Promise((resolve) => {
                const startTime = Date.now();
                const checkCapacitor = () => {
                    if (window.Capacitor && typeof window.Capacitor.isNativePlatform === 'function') {
                        console.log('✅ Capacitor 已載入');
                        resolve(true);
                    } else if (Date.now() - startTime > maxWaitTime) {
                        console.log('⏰ 等待 Capacitor 載入超時');
                        resolve(false);
                    } else {
                        setTimeout(checkCapacitor, 100);
                    }
                };
                checkCapacitor();
            });
        }

        // 增強的文本轉語音函數
        async function speakText(text, forceSpeak = false) {
            if (!text || text.trim() === '') {
                console.log('語音文本為空，跳過播放');
                return;
            }

            if (!speechEnabled && !forceSpeak) {
                console.log('語音已被用戶禁用');
                return;
            }

            // 記錄用戶互動
            recordUserInteraction();

            // 根據環境選擇語音合成方式
            if (isCapacitorEnvironment()) {
                try {
                    await performCapacitorSpeech(text);
                } catch (error) {
                    console.warn('❌ Capacitor TTS 失敗，嘗試瀏覽器 TTS:', error);
                    // 在 Android App 中也嘗試瀏覽器 TTS 作為回退
                    await performBrowserSpeech(text);
                }
            } else {
                await performBrowserSpeech(text);
            }
        }

        // 獲取最佳中文語音
        async function getBestChineseVoice() {
            try {
                const { TextToSpeech } = Capacitor.Plugins;
                const result = await TextToSpeech.getSupportedVoices();
                const voices = result.voices || [];
                
                console.log('🎵 可用語音列表:', voices);
                
                // 尋找中文語音，優先選擇台灣或香港的語音
                const chineseVoices = voices.filter(voice => 
                    voice.lang && (
                        voice.lang.includes('zh-TW') || 
                        voice.lang.includes('zh-HK') ||
                        voice.lang.includes('zh-CN') ||
                        voice.lang.includes('zh') ||
                        (voice.name && voice.name.toLowerCase().includes('chinese')) ||
                        (voice.name && voice.name.toLowerCase().includes('mandarin'))
                    )
                );
                
                console.log('🎵 找到的中文語音:', chineseVoices);
                
                if (chineseVoices.length > 0) {
                    // 優先選擇台灣語音
                    const taiwanVoice = chineseVoices.find(voice => 
                        voice.lang && voice.lang.includes('zh-TW')
                    );
                    if (taiwanVoice) {
                        console.log('✅ 選擇台灣中文語音:', taiwanVoice.name);
                        return taiwanVoice.name;
                    }
                    
                    // 其次選擇香港語音
                    const hkVoice = chineseVoices.find(voice => 
                        voice.lang && voice.lang.includes('zh-HK')
                    );
                    if (hkVoice) {
                        console.log('✅ 選擇香港中文語音:', hkVoice.name);
                        return hkVoice.name;
                    }
                    
                    // 最後選擇任何中文語音
                    console.log('✅ 選擇中文語音:', chineseVoices[0].name);
                    return chineseVoices[0].name;
                }
                
                console.warn('⚠️ 未找到中文語音，使用預設語音');
                return null;
            } catch (error) {
                console.warn('⚠️ 獲取語音列表失敗:', error);
                return null;
            }
        }

        // 使用 Capacitor TTS 進行語音播放
        async function performCapacitorSpeech(text) {
            try {
                // 使用 Capacitor 全局對象訪問 TTS 插件
                const { TextToSpeech } = Capacitor.Plugins;

                // 嘗試簡單的 TTS 調用，不依賴 getSupportedVoices
                console.log('🎵 嘗試直接使用 Capacitor TTS 播放');

                // 根據當前界面語言動態調整語言選項優先順序
                const currentLang = getCurrentLanguage();
                let languageOptions = [];

                // 根據當前語言設定優先順序
                if (currentLang === 'en-US' || currentLang === 'en') {
                    languageOptions = ['en-US', 'en', 'zh-TW', 'zh-CN', 'zh'];
                } else if (currentLang === 'ja-JP' || currentLang === 'ja') {
                    languageOptions = ['ja-JP', 'ja', 'zh-TW', 'zh-CN', 'zh', 'en-US'];
                } else {
                    languageOptions = ['zh-TW', 'zh-CN', 'zh', 'en-US'];
                }

                console.log(`🌐 當前界面語言: ${currentLang}, TTS 語言優先順序:`, languageOptions);
                let success = false;

                for (const lang of languageOptions) {
                    try {
                        const basicOptions = {
                            text: text,
                            lang: lang,
                            rate: 1.0,
                            pitch: 1.0,
                            volume: 1.0
                        };

                        console.log(`🎵 嘗試語言 ${lang}，語音設定:`, basicOptions);
                        await TextToSpeech.speak(basicOptions);
                        console.log(`✅ Capacitor TTS 播放完成 (${lang})`);
                        success = true;
                        break;
                    } catch (langError) {
                        console.warn(`❌ 語言 ${lang} 失敗:`, langError);
                        continue;
                    }
                }

                if (!success) {
                    throw new Error('所有語言選項都失敗');
                }
            } catch (error) {
                console.warn('❌ Capacitor TTS 播放失敗:', error);

                // 檢查是否是 TTS 引擎不可用的問題
                if (error.message && error.message.includes('Not yet initialized')) {
                    console.log('🔍 檢測到 TTS 引擎問題，提供安裝指引');
                    showTTSInstallationGuide();
                }

                throw error; // 拋出錯誤，讓上層處理回退
            }
        }

        // 顯示 TTS 安裝指引
        function showTTSInstallationGuide() {
            const guideMessage = `
🔊 語音播放需要 TTS 引擎

您的設備沒有安裝文字轉語音引擎。
請安裝以下任一 TTS 應用：

📱 推薦選項：
• Google 文字轉語音
• Samsung 文字轉語音
• 訊飛語音合成

📋 安裝步驟：
1. 打開 Google Play 商店
2. 搜尋 "Google 文字轉語音"
3. 安裝並設為預設 TTS 引擎
4. 重新啟動本應用

💡 安裝後即可享受語音反饋功能！
            `.trim();

            showNotification(guideMessage, 'info', 10000);

            // 記錄 TTS 引擎缺失事件
            console.log('📋 已顯示 TTS 安裝指引');
        }

        // 使用瀏覽器語音合成進行語音播放
        async function performBrowserSpeech(text) {
            console.log('🔊 嘗試使用瀏覽器語音合成播放:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));
            
            // 檢查瀏覽器是否支援語音合成
            if (!window.speechSynthesis) {
                console.warn('瀏覽器不支援語音合成功能');
                // 在 Android WebView 中，語音合成可能不可用
                // 提供文字反饋作為替代
                showNotification('語音播放不可用，請查看文字內容', 'warning');
                return;
            }

            // 如果已有語音正在播放，先停止
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
                // 等待一小段時間確保停止完成
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            await performSpeech(text);
        }

        // 執行語音播放的核心函數
        function performSpeech(text) {
            return new Promise((resolve, reject) => {
                try {
                    const utterance = new SpeechSynthesisUtterance(text);

                    // 根據當前語言設定調整語音語言
                    const currentLang = getCurrentLanguage();
                    console.log(`🌐 瀏覽器 TTS 當前語言: ${currentLang}`);

                    if (currentLang === 'en' || currentLang === 'en-US') {
                        utterance.lang = 'en-US';
                        console.log('🔊 設定瀏覽器 TTS 語言為英文 (en-US)');
                    } else if (currentLang === 'ja' || currentLang === 'ja-JP') {
                        utterance.lang = 'ja-JP';
                        console.log('🔊 設定瀏覽器 TTS 語言為日文 (ja-JP)');
                    } else {
                        utterance.lang = 'zh-TW';
                        console.log('🔊 設定瀏覽器 TTS 語言為中文 (zh-TW)');
                    }

                    utterance.rate = 0.9; // 稍微慢一點，更清楚
                    utterance.pitch = 1.0;
                    utterance.volume = 0.8;

                    // 添加成功和錯誤處理
                    utterance.onstart = function() {
                        console.log('✅ 瀏覽器語音開始播放:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));
                    };

                    utterance.onend = function() {
                        console.log('✅ 瀏覽器語音播放完成');
                        resolve();
                    };

                    utterance.onerror = function(event) {
                        // 'interrupted' 錯誤是正常的，當新的語音開始時會中斷舊的語音
                        if (event.error === 'interrupted') {
                            console.log('ℹ️ 瀏覽器語音播放被中斷（正常情況）');
                            resolve(); // 將中斷視為正常完成
                        } else {
                            console.warn('❌ 瀏覽器語音播放失敗:', event.error);
                            reject(new Error(event.error));
                        }
                    };

                    // 等待語音引擎準備就緒
                    if (speechSynthesis.getVoices().length === 0) {
                        speechSynthesis.addEventListener('voiceschanged', function() {
                            speechSynthesis.speak(utterance);
                        }, { once: true });
                    } else {
                        speechSynthesis.speak(utterance);
                    }

                } catch (error) {
                     console.error('❌ 瀏覽器語音播放異常:', error);
                     reject(error);
                 }
             });
         }

        function setupEventListeners() {
            // 語言切換
            const languageBtn = document.querySelector('.language-btn');
            const languageDropdown = document.querySelector('.language-dropdown');
            
            languageBtn.addEventListener('click', function() {
                languageDropdown.style.display = 
                    languageDropdown.style.display === 'block' ? 'none' : 'block';
            });

            // 語言選項
            document.querySelectorAll('.language-option').forEach(option => {
                option.addEventListener('click', function() {
                    const lang = this.dataset.lang;
                    switchLanguage(lang);
                    languageDropdown.style.display = 'none';
                });
            });

            // 點餐按鈕
            processBtn.addEventListener('click', function() {
                recordUserInteraction();
                processOrder();
            });

            // 語音按鈕 - 使用新的語音管理器
            voiceBtn.addEventListener('click', function() {
                recordUserInteraction();
                toggleNewVoiceRecording();
            });
            stopVoiceBtn.addEventListener('click', function() {
                recordUserInteraction();
                stopNewVoiceRecording();
            });

            // 繼續點餐
            document.getElementById('continueOrderBtn').addEventListener('click', function() {
                // 提取當前訂單項目
                const currentItems = extractCurrentOrderItems();
                
                // 設置修改模式標記，保存當前訂單狀態
                sessionStorage.setItem('isModifyingOrder', 'true');
                sessionStorage.setItem('currentOrderItems', JSON.stringify(currentItems));
                
                // 清空輸入框並聚焦
                userInput.value = '';
                userInput.focus();
                
                // 顯示提示訊息
                showNotification(getTranslation('continue_adding_meals'), 'info');
            });

            // 確認訂單
            document.getElementById('confirmOrderBtn').addEventListener('click', showOrderSummary);

            // 返回修改
            document.getElementById('backToEditBtn').addEventListener('click', function() {
                orderSummary.classList.remove('show');
                resultsSection.classList.add('show');
            });

            // 最終確認
            document.getElementById('finalConfirmBtn').addEventListener('click', finalizeOrder);

            // 點擊外部關閉語言選單
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.language-switcher')) {
                    languageDropdown.style.display = 'none';
                }
            });
        }

        async function initializeSpeechRecognition() {
            console.log('🚀 開始初始化新的語音管理器...');

            // 等待語音管理器載入
            let retryCount = 0;
            const maxRetries = 50; // 5秒超時

            while (!window.speechManager && retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retryCount++;
            }

            if (!window.speechManager) {
                console.error('❌ 語音管理器載入失敗');
                return;
            }

            console.log('✅ 語音管理器已載入，開始初始化...');

            try {
                // 初始化語音管理器
                await window.speechManager.init();
                console.log('✅ 語音管理器初始化完成');

                // 顯示初始化狀態
                const status = window.speechManager.getStatus();
                console.log('📊 語音管理器狀態:', status);

            } catch (error) {
                console.error('❌ 語音管理器初始化失敗:', error);
            }
        }

        // 舊的語音識別函數已移除，統一使用新的語音管理器
        



        // 舊的語音函數已停用，統一使用新的語音管理器
        function toggleVoiceRecording() {
            console.log('⚠️ 舊的語音函數被調用，重定向到新的語音管理器');
            toggleNewVoiceRecording();
        }

        function startVoiceRecording() {
            console.log('⚠️ 舊的語音啟動函數被調用，重定向到新的語音管理器');
            startNewVoiceRecording();
        }

        function stopVoiceRecording() {
            console.log('⚠️ 舊的語音停止函數被調用，重定向到新的語音管理器');
            stopNewVoiceRecording();
        }

        // 新的語音控制函數 - 使用統一語音管理器
        function toggleNewVoiceRecording() {
            console.log('🎤 切換語音識別狀態...');

            if (!window.speechManager) {
                console.error('❌ 語音管理器未初始化');
                showNotification('語音功能不可用', 'error');
                return;
            }

            const status = window.speechManager.getStatus();

            if (status.isRecognizing) {
                stopNewVoiceRecording();
            } else {
                startNewVoiceRecording();
            }
        }

        function startNewVoiceRecording() {
            console.log('🚀 啟動新語音識別...');

            if (!window.speechManager) {
                console.error('❌ 語音管理器未初始化');
                showNotification('語音功能不可用', 'error');
                return;
            }

            // 設置回調函數
            window.speechManager.setCallbacks({
                onResult: (transcript, isFinal) => {
                    console.log('📝 語音識別結果:', transcript, '最終:', isFinal);
                    console.log('🚀 DEBUG: onResult 回調函數被調用了！v8');

                    const userInput = document.getElementById('userInput');
                    console.log('🔍 檢查輸入框元素:', userInput);
                    console.log('🔍 文字內容:', transcript, '長度:', transcript.length);
                    console.log('🔍 文字去空白後:', transcript.trim(), '長度:', transcript.trim().length);

                    if (userInput) {
                        if (transcript.trim()) {
                            // 無論是否為最終結果，都更新輸入框
                            console.log('✅ 更新輸入框，舊值:', userInput.value);
                            userInput.value = transcript;
                            console.log('✅ 更新輸入框，新值:', userInput.value);

                            if (isFinal) {
                                // 最終結果，處理訂單
                                showNotification('語音識別完成: ' + transcript, 'success');

                                // 自動提交訂單
                                setTimeout(() => {
                                    processOrder();
                                }, 500);
                            } else {
                                // 部分結果，顯示在狀態中
                                showNotification('正在識別: ' + transcript, 'info');
                            }
                        } else {
                            console.log('⚠️ 文字為空，不更新輸入框');
                        }
                    } else {
                        console.error('❌ 找不到 userInput 元素');
                    }
                },

                onError: (error) => {
                    console.error('🚫 語音識別錯誤:', error);
                    showNotification('語音識別錯誤: ' + error.message, 'error');
                    isRecording = false;
                    updateVoiceUI();
                },

                onStart: () => {
                    console.log('🎤 語音識別開始');
                    isRecording = true;
                    updateVoiceUI();
                    showNotification('開始語音識別，請說話...', 'info');
                },

                onEnd: () => {
                    console.log('🔚 語音識別結束');
                    isRecording = false;
                    updateVoiceUI();
                },

                onStatusUpdate: (status) => {
                    console.log('📊 語音狀態更新:', status);
                    switch(status) {
                        case 'listening':
                            showNotification('正在聆聽...', 'info');
                            break;
                        case 'stopped':
                            showNotification('語音識別已停止', 'info');
                            break;
                        case 'fallback':
                            showNotification('已切換到文字輸入模式', 'warning');
                            break;
                    }
                }
            });

            // 啟動語音識別
            window.speechManager.start().then(success => {
                if (!success) {
                    console.error('❌ 語音識別啟動失敗');
                    showNotification('語音識別啟動失敗', 'error');
                }
            });
        }

        function stopNewVoiceRecording() {
            console.log('🛑 停止新語音識別...');

            if (window.speechManager) {
                window.speechManager.stop();
            }

            isRecording = false;
            updateVoiceUI();
            showNotification('語音識別已停止', 'info');
        }
        
        function updateVoiceUI() {
            if (voiceBtn) {
                if (isRecording) {
                    voiceBtn.classList.add('recording');
                    voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
                    voiceBtn.title = '停止語音識別';
                } else {
                    voiceBtn.classList.remove('recording');
                    voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                    voiceBtn.title = '開始語音識別';
                }
            }
            
            if (voiceStatus) {
                if (isRecording) {
                    voiceStatus.classList.add('active');
                } else {
                    voiceStatus.classList.remove('active');
                }
            }
        }

        async function processOrder() {
            const input = userInput.value.trim();
            
            if (!input) {
                showNotification(getTranslation('please_enter_meal_request'), 'error');
                return;
            }

            // 檢查是否為修改模式
            const isModifyingOrder = sessionStorage.getItem('isModifyingOrder') === 'true';
            let finalInput = input;

            if (isModifyingOrder) {
                // 獲取當前訂單項目
                const currentOrderItems = JSON.parse(sessionStorage.getItem('currentOrderItems') || '[]');

                if (currentOrderItems.length > 0) {
                    // 構建包含現有訂單和修改請求的完整提示
                    const currentOrderSummary = currentOrderItems.join('\n');
                    finalInput = `我目前的訂單包含：\n${currentOrderSummary}\n\n現在我想要添加：${input}`;

                    console.log('修改模式 - 原始訂單:', currentOrderItems);
                    console.log('修改模式 - 添加請求:', input);
                    console.log('修改模式 - 完整提示:', finalInput);
                }

                // 清除修改模式標記
                sessionStorage.removeItem('isModifyingOrder');
                sessionStorage.removeItem('currentOrderItems');
            }

            showLoading(getTranslation('analyzing_order_request'));
            processBtn.disabled = true;

            try {
                const response = await fetch('/api/nlp/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        input: finalInput,
                        language: currentLanguage
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    displayResults(result);
                    showNotification(getTranslation('meal_identified_success'), 'success');
                } else {
                    showNotification(result.message || getTranslation('processing_failed_retry'), 'error');
                }
            } catch (error) {
                console.error(getTranslation('processing_error') + ':', error);
                showNotification(getTranslation('network_error_check_connection'), 'error');
            } finally {
                hideLoading();
                processBtn.disabled = false;
            }
        }

        function displayResults(result) {
            console.log('displayResults 被調用，完整結果:', result);
            console.log('result.data:', result.data);
            console.log('result.data.matches:', result.data.matches);
            
            // 顯示 AI 回應
            let cleanResponse = '';
            if (result.analysis && result.analysis.response) {
                // 移除 ORDER_JSON_START 和 ORDER_JSON_END 標記
                cleanResponse = result.analysis.response
                    .replace(/ORDER_JSON_START[\s\S]*?ORDER_JSON_END/g, '')
                    .trim();
                
                // 如果清理後的回應為空，使用預設訊息
                if (!cleanResponse) {
                    cleanResponse = getTranslation('meal_confirmation_message');
                }
                
                aiResponseText.textContent = cleanResponse;
            } else {
                cleanResponse = getTranslation('meal_confirmation_message');
                aiResponseText.textContent = cleanResponse;
            }

            // 生成完整的語音內容，包含餐點信息
            const speechContent = generateSpeechContent(cleanResponse, result.data.matches || []);
            
            // 播放語音回應
            if (speechContent) {
                console.log('🔊 準備播放語音:', speechContent);
                // 延遲播放，確保 DOM 更新完成
                setTimeout(() => {
                    speakText(speechContent);
                }, 500);
            }

            // 顯示識別到的餐點
            displayMenuItems(result.data.matches || []);
            
            // 顯示結果區域
            resultsSection.classList.add('show');
            
            // 滾動到結果區域
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // 統一使用 NT$ 格式化價格（用於顯示）
        function formatPrice(price) {
            // 無論什麼語言都使用 NT$（新台幣）
            return `NT$${price}`;
        }

        // 格式化價格數字（用於語音，翻譯模板中已包含 NT$ 前綴）
        function formatPriceForSpeech(price) {
            // 只返回數字，因為翻譯模板中已經包含 NT$ 前綴
            return price;
        }

        // 生成包含完整點餐信息的語音內容
        function generateSpeechContent(baseResponse, items) {
            if (!items || items.length === 0) {
                return baseResponse;
            }

            const currentLang = getCurrentLanguage();
            console.log(`🌐 生成語音內容，當前語言: ${currentLang}`);

            let speechText = baseResponse + ' ';

            // 添加識別到的餐點信息
            if (items.length === 1) {
                const item = items[0];
                const itemName = getItemName(item);
                const quantity = item.quantity || 1;
                const price = item.price || 0;
                const formattedPrice = formatPriceForSpeech(price);

                const identifiedText = getTranslation('identified_single_item') || 'Identified {quantity} {item} at {price}.';
                speechText += identifiedText
                    .replace('{quantity}', quantity)
                    .replace('{item}', itemName)
                    .replace('{price}', formattedPrice);

                console.log(`🔊 單項語音內容: ${speechText}`);
            } else {
                const identifiedMultipleText = getTranslation('identified_multiple_items') || 'Identified the following items:';
                speechText += identifiedMultipleText;
                items.forEach((item, index) => {
                    const itemName = getItemName(item);
                    const quantity = item.quantity || 1;
                    const price = item.price || 0;
                    const formattedPrice = formatPriceForSpeech(price);

                    const itemText = getTranslation('item_format') || '{quantity} {item} {price}';
                    speechText += ' ' + itemText
                        .replace('{quantity}', quantity)
                        .replace('{item}', itemName)
                        .replace('{price}', formattedPrice);

                    if (index < items.length - 1) {
                        speechText += getTranslation('item_separator') || ', ';
                    }
                });
                speechText += getTranslation('sentence_end') || '.';
                console.log(`🔊 多項語音內容: ${speechText}`);
            }

            // 計算總金額
            const totalAmount = items.reduce((total, item) => {
                return total + ((item.price || 0) * (item.quantity || 1));
            }, 0);

            if (totalAmount > 0) {
                const formattedTotal = formatPriceForSpeech(totalAmount);
                const totalText = getTranslation('total_amount') || 'Total amount: {total}.';
                speechText += ' ' + totalText.replace('{total}', formattedTotal);
                console.log(`🔊 包含總金額的語音內容: ${speechText}`);
            }

            return speechText;
        }

        function displayMenuItems(items) {
            console.log('displayMenuItems 被調用，接收到的 items:', items);
            console.log('items 數量:', items.length);
            menuItemsGrid.innerHTML = '';
            // 不清空 currentOrder，保留現有購物車內容
            // currentOrder = [];

            // 重新分配所有現有項目的索引，確保索引連續且正確
            currentOrder.forEach((existingItem, existingIndex) => {
                existingItem.index = existingIndex;
                const card = createMenuItemCard(existingItem, existingIndex);
                menuItemsGrid.appendChild(card);
            });

            // 然後處理新識別的項目
            items.forEach((item, index) => {
                // 檢查新項目是否已在購物車中
                const existingItemIndex = currentOrder.findIndex(existing => 
                    (existing.name === item.name || existing.name_zh === item.name_zh) && 
                    existing.price === item.price
                );
                
                if (existingItemIndex === -1) {
                    // 新項目不在購物車中，添加它
                    const newIndex = currentOrder.length;
                    
                    // 檢查是否需要尺寸選擇
                    if (needsSizeSelection(item)) {
                        const card = createSizeSelectionCard(item, newIndex);
                        menuItemsGrid.appendChild(card);
                    } else {
                        const card = createMenuItemCard(item, newIndex);
                        menuItemsGrid.appendChild(card);
                        
                        // 添加到當前訂單，使用後端返回的實際數量
                        currentOrder.push({
                            ...item,
                            quantity: item.quantity || 1,
                            index: newIndex
                        });
                    }
                }
            });
         }

        function createMenuItemCard(item, index) {
            const card = document.createElement('div');
            card.className = 'menu-item-card';
            card.dataset.index = index;
            
            // 處理圖片顯示
            const imageUrl = item.image || item.image_url || '';
            const imageContent = imageUrl ? 
                `<img src="${imageUrl}" alt="${getItemName(item)}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">` +
                `<i class="fas fa-utensils" style="display: none;"></i>` :
                `<i class="fas fa-utensils"></i>`;
            
            card.innerHTML = `
                <div class="item-header">
                    <div class="item-image">
                        ${imageContent}
                    </div>
                    <div class="item-info">
                        <div class="item-name">${getItemName(item)}</div>
                        <div class="item-price">${formatPrice(item.price || 0)}</div>
                        <div class="item-description">${item.description || getTranslation('delicious_item')}</div>
                    </div>
                </div>
                <div class="item-controls">
                    <div class="quantity-control">
                        <button class="quantity-btn" onclick="changeQuantity(${index}, -1)">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="quantity-display" id="quantity-${index}">${item.quantity || 1}</span>
                        <button class="quantity-btn" onclick="changeQuantity(${index}, 1)">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="remove-btn" onclick="removeItem(${index})">
                        <i class="fas fa-trash"></i> <span data-i18n="remove_btn"></span>
                    </button>
                </div>
            `;
            
            return card;
        }

        // 從 appPrompt 獲取菜單數據
        let menuData = null;
        
        // 載入菜單數據
        async function loadMenuData() {
            try {
                const response = await fetch('/appPrompt/appPrompt_202506101005.json');
                const data = await response.json();
                menuData = data.parameters.menu;
                console.log('菜單數據載入成功:', menuData);
            } catch (error) {
                console.error('載入菜單數據失敗:', error);
            }
        }

        // 檢查餐點是否需要尺寸選擇
        function needsSizeSelection(item) {
            if (!menuData) {
                console.log('菜單數據未載入，無法檢查尺寸選擇');
                return false;
            }
            
            const itemName = getItemName(item);
            const itemPrice = item.price;
            console.log('檢查餐點是否需要尺寸選擇:', itemName, '價格:', itemPrice);
            
            // 如果餐點已經有明確的價格，說明 AI 已經識別出具體餐點，不需要尺寸選擇
            if (itemPrice && itemPrice > 0) {
                console.log('餐點已有明確價格，無需尺寸選擇:', itemName, '$' + itemPrice);
                return false;
            }
            
            // 如果餐點名稱已經包含尺寸信息（小、中、大），則不需要選擇尺寸
            if (/\([小中大]\)|小杯|中杯|大杯|小份|中份|大份/.test(itemName)) {
                console.log('餐點已包含尺寸信息，無需選擇:', itemName);
                return false;
            }
            
            // 查找所有匹配的餐點
            const matchingItems = [];
            menuData.forEach(category => {
                category.items.forEach(menuItem => {
                    // 移除尺寸標記和括號進行比較
                    const baseItemName = getItemName(menuItem).replace(/\s*\([小中大]\)\s*/g, '').trim();
                    const baseInputName = itemName.replace(/\s*\([小中大]\)\s*/g, '').trim();
                    
                    // 更嚴格的匹配邏輯，避免過度匹配
                    const isMatch = 
                        baseItemName === baseInputName || 
                        // 只有當輸入名稱較短且完全包含在菜單項目中時才匹配
                        (baseInputName.length >= 2 && baseItemName.includes(baseInputName)) ||
                        // 特殊處理常見簡稱
                        (baseInputName.includes('可樂') && baseItemName.includes('可口可樂')) ||
                        (baseInputName.includes('雪碧') && baseItemName.includes('雪碧'));
                    
                    if (isMatch) {
                        matchingItems.push(menuItem);
                        console.log('找到匹配餐點:', getItemName(menuItem));
                    }
                });
            });
            
            console.log('匹配的餐點數量:', matchingItems.length);
            // 只有當找到多個不同尺寸的相同餐點且用戶沒有指定尺寸時，才需要尺寸選擇
            const needsSelection = matchingItems.length > 1;
            console.log('是否需要尺寸選擇:', needsSelection);
            return needsSelection;
        }

        // 創建尺寸選擇卡片
        function createSizeSelectionCard(item, index) {
            const card = document.createElement('div');
            card.className = 'menu-item-card size-selection-card';
            card.dataset.index = index;
            
            // 處理圖片顯示
            const imageUrl = item.image || item.image_url || '';
            const imageContent = imageUrl ? 
                `<img src="${imageUrl}" alt="${getItemName(item)}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">` +
                `<i class="fas fa-utensils" style="display: none;"></i>` :
                `<i class="fas fa-utensils"></i>`;
            
            // 生成尺寸選項
            const sizeOptions = generateSizeOptions(item);
            const sizeOptionsHTML = sizeOptions.map(option => 
                `<button class="size-option-btn" onclick="selectSize(${index}, '${option.size}', ${option.price}, '${option.fullName}', '${option.imageUrl}')">
                    <span class="size-name">${option.size}</span>
                    <span class="size-price">${formatPrice(option.price)}</span>
                </button>`
            ).join('');
            
            card.innerHTML = `
                <div class="item-header">
                    <div class="item-image">
                        ${imageContent}
                    </div>
                    <div class="item-info">
                        <div class="item-name">${getItemName(item)}</div>
                        <div class="item-description">${item.description || getTranslation('delicious_item')}</div>
                        <div class="size-selection-prompt">
                            <i class="fas fa-info-circle"></i> <span data-i18n="please_select_size"></span>
                        </div>
                    </div>
                </div>
                <div class="size-selection-controls">
                    ${sizeOptionsHTML}
                </div>
            `;
            
            return card;
        }

        // 生成尺寸選項
        function generateSizeOptions(item) {
            if (!menuData) {
                console.log('菜單數據未載入，無法生成尺寸選項');
                return [];
            }
            
            const itemName = getItemName(item);
            console.log('為餐點生成尺寸選項:', itemName);
            const options = [];
            const matchingItems = [];
            
            menuData.forEach(category => {
                category.items.forEach(menuItem => {
                    // 移除尺寸標記和括號進行比較
                    const baseItemName = getItemName(menuItem).replace(/\s*\([小中大]\)\s*/g, '').trim();
                    const baseInputName = itemName.replace(/\s*\([小中大]\)\s*/g, '').trim();
                    
                    // 使用與 needsSizeSelection 相同的嚴格匹配邏輯
                    const isMatch = 
                        baseItemName === baseInputName || 
                        // 只有當輸入名稱較短且完全包含在菜單項目中時才匹配
                        (baseInputName.length >= 2 && baseItemName.includes(baseInputName)) ||
                        // 特殊處理常見簡稱
                        (baseInputName.includes('可樂') && baseItemName.includes('可口可樂')) ||
                        (baseInputName.includes('雪碧') && baseItemName.includes('雪碧'));
                    
                    if (isMatch) {
                        matchingItems.push(menuItem);
                        console.log('找到匹配的尺寸選項:', getItemName(menuItem), '$' + menuItem.price);
                    }
                });
            });
            
            // 如果沒有找到匹配項或只找到一個項目，返回空數組
            if (matchingItems.length <= 1) {
                console.log('沒有找到多個尺寸選項，不需要尺寸選擇');
                return [];
            }
            
            // 按尺寸排序並生成選項
            const sizeOrder = { '小': 1, '中': 2, '大': 3 };
            matchingItems.sort((a, b) => {
                const sizeA = getItemName(a).match(/\(([小中大])\)/)?.[1] || '中';
                const sizeB = getItemName(b).match(/\(([小中大])\)/)?.[1] || '中';
                return (sizeOrder[sizeA] || 2) - (sizeOrder[sizeB] || 2);
            });
            
            matchingItems.forEach(menuItem => {
                const sizeMatch = getItemName(menuItem).match(/\(([小中大])\)/);
                const size = sizeMatch ? sizeMatch[1] : '標準';
                options.push({ 
                    size: size, 
                    price: menuItem.price,
                    fullName: getItemName(menuItem),
                    imageUrl: menuItem.image_url || ''
                });
            });
            
            console.log('生成的尺寸選項:', options);
            return options;
        }

        // 選擇尺寸
        function selectSize(index, size, price, fullName, imageUrl) {
            const card = document.querySelector(`[data-index="${index}"]`);
            if (!card) return;
            
            // 獲取原始餐點信息
            const originalItem = {
                name: card.querySelector('.item-name').textContent,
                description: card.querySelector('.item-description').textContent,
                image: imageUrl || card.querySelector('img')?.src || '',
                image_url: imageUrl || card.querySelector('img')?.src || ''
            };
            
            // 創建帶尺寸的新餐點
            const currentLang = getCurrentLanguage();
            const newItem = {
                ...originalItem,
                name: fullName || `${originalItem.name}（${size}）`,
                price: price,
                quantity: 1,
                size: size,
                index: index
            };
            
            // 根據當前語言設定對應的名稱欄位
            if (currentLang === 'en-US' || currentLang === 'en') {
                newItem.name_en = fullName || `${originalItem.name}（${size}）`;
            } else if (currentLang === 'ja-JP' || currentLang === 'ja') {
                newItem.name_jp = fullName || `${originalItem.name}（${size}）`;
            } else {
                newItem.name_zh = fullName || `${originalItem.name}（${size}）`;
            }
            
            // 替換卡片為普通餐點卡片
            const newCard = createMenuItemCard(newItem, index);
            card.parentNode.replaceChild(newCard, card);
            
            // 添加到當前訂單
            currentOrder.push(newItem);
            
            // 顯示成功訊息
            showNotification(`${getTranslation('item_selected')} ${newItem.name}`, 'success');
        }

        function changeQuantity(index, change) {
            const orderItem = currentOrder.find(item => item.index === index);
            if (orderItem) {
                orderItem.quantity = Math.max(1, orderItem.quantity + change);
                document.getElementById(`quantity-${index}`).textContent = orderItem.quantity;
            }
        }

        function removeItem(index) {
            const card = document.querySelector(`[data-index="${index}"]`);
            if (card) {
                card.style.opacity = '0.5';
                setTimeout(() => {
                    card.remove();
                    currentOrder = currentOrder.filter(item => item.index !== index);
                    
                    if (currentOrder.length === 0) {
                        resultsSection.classList.remove('show');
                        showNotification(getTranslation('order_cleared'), 'success');
                    }
                }, 300);
            }
        }

        function showOrderSummary() {
            if (currentOrder.length === 0) {
                showNotification(getTranslation('order_empty_please_order'), 'error');
                return;
            }

            // 生成訂單摘要
            let summaryHTML = '';
            let total = 0;

            currentOrder.forEach(item => {
                const itemTotal = (item.price || 0) * item.quantity;
                total += itemTotal;
                
                summaryHTML += `
                    <div class="summary-item">
                        <span>${getItemName(item)} × ${item.quantity}</span>
                        <span>${formatPrice(itemTotal)}</span>
                    </div>
                `;
            });

            summaryHTML += `
                <div class="summary-item">
                    <span data-i18n="total"></span>
                    <span>${formatPrice(total)}</span>
                </div>
            `;

            summaryItems.innerHTML = summaryHTML;
            
            // 顯示訂單摘要
            resultsSection.classList.remove('show');
            orderSummary.classList.add('show');
            
            // 滾動到摘要區域
            orderSummary.scrollIntoView({ behavior: 'smooth' });
        }

        async function finalizeOrder() {
            showLoading(getTranslation('processing_order'));
            
            try {
                const response = await fetch('/api/order/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        items: currentOrder,
                        language: currentLanguage
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification(getTranslation('order_submitted_success'), 'success');
                    
                    // 重置界面
                    setTimeout(() => {
                        resetInterface();
                    }, 2000);
                } else {
                    showNotification(result.message || getTranslation('order_processing_failed'), 'error');
                }
            } catch (error) {
                console.error('訂單處理錯誤:', error);
                showNotification(getTranslation('network_error_retry'), 'error');
            } finally {
                hideLoading();
            }
        }

        function resetInterface() {
            userInput.value = '';
            currentOrder = [];
            resultsSection.classList.remove('show');
            orderSummary.classList.remove('show');
            menuItemsGrid.innerHTML = '';
            summaryItems.innerHTML = '';
        }

        // 提取當前訂單項目
        function extractCurrentOrderItems() {
            const items = [];
            
            // 從當前顯示的餐點卡片中提取項目
            currentOrder.forEach(item => {
                if (item.quantity > 0) {
                    const itemName = getItemName(item);
                    const itemPrice = item.price || 0;
                    const itemQuantity = item.quantity || 1;
                    const itemTotal = itemPrice * itemQuantity;
                    
                    items.push(`${itemName} x ${itemQuantity} - NT$${itemTotal}`);
                }
            });
            
            console.log('提取到的當前訂單項目:', items);
            return items;
        }

        function switchLanguage(lang) {
            // 映射語言代碼到標準格式
            const langMap = {
                'zh-TW': 'zh-TW',
                'en': 'en-US',
                'ja': 'ja-JP'
            };
            
            const mappedLang = langMap[lang] || 'zh-TW';
            
            // 檢查是否有 language-resources.js 中的函數可用
            // 使用不同的檢查方式避免遞迴調用
            if (window.languageResources && typeof window.initializeLanguage === 'function') {
                // 直接調用 language-resources.js 中的邏輯
                localStorage.setItem('preferredLanguage', mappedLang);
                currentLanguage = mappedLang;  // 確保 currentLanguage 使用正確的語言代碼
                
                // 更新會話管理器的語言設定
                if (window.sessionManager) {
                    window.sessionManager.setLanguage(mappedLang);
                }
                
                window.initializeLanguage();
                
                // 更新語音識別語言設定
                if (window.speechRecognitionInstance) {
                    if (mappedLang === 'en-US') {
                        window.speechRecognitionInstance.lang = 'en-US';
                    } else if (mappedLang === 'ja-JP') {
                        window.speechRecognitionInstance.lang = 'ja-JP';
                    } else {
                        window.speechRecognitionInstance.lang = 'zh-TW';
                    }
                }
                
                // 延遲更新動態生成的內容
                setTimeout(() => {
                    if (typeof window.updatePageLanguage === 'function') {
                        window.updatePageLanguage();
                    }
                }, 100);
                
            } else {
                // 備用方案：直接更新 localStorage 和當前語言
                currentLanguage = mappedLang;  // 使用映射後的語言代碼
                localStorage.setItem('preferredLanguage', mappedLang);
                
                // 更新語音識別語言
                if (recognition) {
                    recognition.lang = mappedLang;
                }
                
                console.warn('language-resources.js 尚未載入，僅設置了基本配置');
            }
            
            // 顯示多語言通知
            const langNames = {
                'zh-TW': '繁體中文',
                'en': 'English',
                'ja': '日本語'
            };
            const switchMessages = {
                'zh-TW': `已切換至 ${langNames[lang]}`,
                'en': `Switched to ${langNames[lang]}`,
                'ja': `${langNames[lang]}に切り替えました`
            };
            showNotification(switchMessages[lang] || switchMessages['zh-TW'], 'success');
        }
        
        // updatePageLanguage 函數已移至 language-resources.js 中統一管理

        function showLoading(text = '載入中...') {
            loadingText.textContent = text;
            loadingOverlay.classList.add('show');
        }

        function hideLoading() {
            loadingOverlay.classList.remove('show');
        }

        function showNotification(text, type = 'success') {
            notificationText.textContent = text;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // ========== TTS 功能 ==========
        
        // 檢測 TTS 支援
        function detectTTSSupport() {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            const isAndroidWebView = /Android/.test(userAgent) && /wv/.test(userAgent);
            const hasSpeechSynthesis = 'speechSynthesis' in window && window.speechSynthesis;
            const isCapacitor = isCapacitorEnvironment();
            
            return {
                isAndroidWebView,
                hasSpeechSynthesis,
                isCapacitor,
                shouldUseFallback: !isCapacitor && (isAndroidWebView || !hasSpeechSynthesis)
            };
        }
        
        // 備用 TTS 功能（顯示文字通知）
        function fallbackTTS(text) {
            console.log('使用備用 TTS:', text);
            showNotification(`🔊 ${text}`, 'info', 3000);
            
            // 可選：震動反饋（如果支援）
            if ('vibrate' in navigator) {
                navigator.vibrate([100, 50, 100]);
            }
        }
        
        async function testTTS(text = '您好，這是語音測試，TTS 功能正常工作') {
            console.log('🎤 開始 TTS 測試');
            
            const ttsSupport = detectTTSSupport();
            console.log('TTS 支援檢測:', ttsSupport);
            
            // 如果不支援原生 TTS，使用備用方案
            if (ttsSupport.shouldUseFallback) {
                console.log('使用備用 TTS 方案');
                fallbackTTS(text);
                return;
            }
            
            try {
                // 停止任何正在進行的語音
                window.speechSynthesis.cancel();
                
                // 創建語音合成實例
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-TW';
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                utterance.volume = 1.0;
                
                // 設置事件監聽器
                utterance.onstart = () => {
                    console.log('🎵 語音開始播放');
                    showNotification(getTranslation('tts_test_start'), 'info');
                };
                
                utterance.onend = () => {
                    console.log('✅ 語音播放完成');
                    showNotification(getTranslation('tts_test_complete'), 'success');
                };
                
                utterance.onerror = (event) => {
                    console.error('❌ 語音播放錯誤:', event.error);
                    showNotification(getTranslation('tts_unavailable_text_display'), 'warning');
                    // 降級到備用方案
                    fallbackTTS(text);
                };
                
                // 開始語音合成
                window.speechSynthesis.speak(utterance);
                
                // 設置超時保護
                setTimeout(() => {
                    if (window.speechSynthesis.speaking) {
                        console.log('TTS 超時，停止播放');
                        window.speechSynthesis.cancel();
                        fallbackTTS(text);
                    }
                }, 5000);
                
            } catch (error) {
                console.error('❌ TTS 測試失敗:', error);
                fallbackTTS(text);
            }
        }

        // 將 testTTS 函數暴露到全域
        window.testTTS = testTTS;

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== Docker 版本 TTS 診斷 ===');
            console.log('Capacitor:', typeof window.Capacitor);
            console.log('testTTS:', typeof window.testTTS);
            console.log('speechSynthesis:', typeof window.speechSynthesis);
            
            // 測試瀏覽器 TTS
            if (window.speechSynthesis) {
                const utterance = new SpeechSynthesisUtterance('測試');
                utterance.lang = 'zh-TW';
                window.speechSynthesis.speak(utterance);
                console.log('✅ TTS 測試已啟動');
            }
        });
    </script>

    <!-- 浮動 TTS 測試按鈕 -->
    <button class="floating-tts-btn" onclick="testTTS()" title="TTS Test">
        <i class="fas fa-volume-up"></i>
    </button>
</body>
</html>