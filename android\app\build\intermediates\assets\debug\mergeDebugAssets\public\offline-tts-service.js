class OfflineTTSService {
    constructor() {
        this.isInitialized = false;
        this.currentAudio = null;
        this.tts = null;
        this.supportedVoices = {
            'zh': 'zh_CN-huayan-medium',
            'zh-TW': 'zh_CN-huayan-medium', 
            'zh-CN': 'zh_CN-huayan-medium',
            'en': 'en_US-hfc_female-medium',
            'en-US': 'en_US-hfc_female-medium',
            'ja': 'en_US-hfc_female-medium' // 暫時使用英文語音，因為可能沒有日文模型
        };
    }

    async initialize() {
        try {
            console.log('🔧 初始化離線 TTS 服務...');
            
            // 等待 CDN 版本加載完成
            let retries = 0;
            while (!window.VITS && retries < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retries++;
            }
            
            if (!window.VITS) {
                throw new Error('VITS 庫加載失敗');
            }
            
            this.tts = window.VITS;
            
            // 預載入常用語音模型
            await this.preloadVoices();
            
            this.isInitialized = true;
            console.log('✅ 離線 TTS 服務初始化成功');
            return true;
        } catch (error) {
            console.error('❌ 離線 TTS 服務初始化失敗:', error);
            return false;
        }
    }

    async preloadVoices() {
        try {
            console.log('🔄 預載入語音模型...');
            
            // 獲取可用語音列表
            const voices = await this.tts.voices();
            console.log('📋 可用語音模型:', Object.keys(voices));
            
            // 預載入中文和英文模型
            const voicesToPreload = ['zh_CN-huayan-medium', 'en_US-hfc_female-medium'];
            
            for (const voiceId of voicesToPreload) {
                if (voices[voiceId]) {
                    console.log(`⬇️ 下載語音模型: ${voiceId}`);
                    await this.tts.download(voiceId, (progress) => {
                        console.log(`📥 下載進度 ${voiceId}: ${Math.round(progress.loaded * 100 / progress.total)}%`);
                    });
                    console.log(`✅ 語音模型下載完成: ${voiceId}`);
                }
            }
        } catch (error) {
            console.warn('⚠️ 預載入語音模型失敗，將在使用時下載:', error);
        }
    }

    detectLanguage(text) {
        // 簡單的語言檢測邏輯
        const chineseRegex = /[\u4e00-\u9fff]/;
        const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
        
        if (chineseRegex.test(text)) {
            return 'zh-CN';
        } else if (japaneseRegex.test(text)) {
            return 'ja';
        } else {
            return 'en-US';
        }
    }

    async speak(text, language = null) {
        try {
            if (!this.isInitialized || !this.tts) {
                throw new Error('TTS 服務尚未初始化');
            }

            // 停止當前播放
            this.stop();

            // 檢測或使用指定語言
            const targetLanguage = language || this.detectLanguage(text);
            const voiceId = this.supportedVoices[targetLanguage] || this.supportedVoices['en-US'];
            
            console.log(`🎵 使用離線 TTS 播放: ${text} (語言: ${targetLanguage}, 語音: ${voiceId})`);

            // 使用 vits-web 進行語音合成
            const wav = await this.tts.predict({
                text: text,
                voiceId: voiceId
            });

            // 創建音頻對象並播放
            this.currentAudio = new Audio();
            this.currentAudio.src = URL.createObjectURL(wav);
            
            // 播放完成後清理
            this.currentAudio.onended = () => {
                URL.revokeObjectURL(this.currentAudio.src);
                this.currentAudio = null;
            };
            
            // 播放音頻
            await this.currentAudio.play();
            console.log('🔊 離線 TTS 播放開始');

        } catch (error) {
            console.error('❌ 離線 TTS 播放失敗:', error);
            throw error;
        }
    }

    stop() {
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio.currentTime = 0;
            if (this.currentAudio.src) {
                URL.revokeObjectURL(this.currentAudio.src);
            }
            this.currentAudio = null;
            console.log('⏹️ 停止離線 TTS 播放');
        }
    }

    isPlaying() {
        return this.currentAudio && !this.currentAudio.paused;
    }

    isReady() {
        return this.isInitialized && this.tts !== null;
    }
}

// 創建全局實例
window.offlineTTSService = new OfflineTTSService();