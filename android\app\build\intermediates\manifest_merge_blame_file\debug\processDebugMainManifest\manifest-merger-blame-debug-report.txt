1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aios.app.ordering"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- suppress AndroidElementNotAllowed -->
12    <queries>
12-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:5:5-12:15
13        <intent>
13-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:6:9-8:18
14            <action android:name="android.speech.RecognitionService" />
14-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:7:13-72
14-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:7:21-69
15        </intent>
16        <intent>
16-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:9:9-11:18
17            <action android:name="android.intent.action.TTS_SERVICE" />
17-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:10:13-72
17-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:10:21-69
18        </intent>
19        <intent>
19-->[:capacitor-camera] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
20            <action android:name="android.media.action.IMAGE_CAPTURE" />
20-->[:capacitor-camera] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
20-->[:capacitor-camera] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
21        </intent>
22    </queries>
23
24    <!-- Permissions -->
25
26    <uses-permission android:name="android.permission.INTERNET" />
26-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:50:5-67
26-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:50:22-64
27    <uses-permission android:name="android.permission.RECORD_AUDIO" />
27-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:51:5-71
27-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:51:22-68
28    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
28-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:52:5-80
28-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:52:22-77
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:53:5-68
29-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:53:22-65
30    <uses-permission android:name="android.permission.VIBRATE" />
30-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:54:5-66
30-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:54:22-63
31
32    <!-- 語音辨識相關權限 -->
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:57:5-79
33-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:57:22-76
34    <uses-permission
34-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:58:5-108
35        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
35-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:58:22-78
36        android:maxSdkVersion="28" />
36-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:58:79-105
37
38    <permission
38-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
39        android:name="com.aios.app.ordering.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.aios.app.ordering.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:14:5-46:19
45        android:allowBackup="true"
45-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:15:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:16:9-43
50        android:label="@string/app_name"
50-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:17:9-41
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:18:9-54
52        android:supportsRtl="true"
52-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:19:9-35
53        android:testOnly="true"
54        android:theme="@style/AppTheme" >
54-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:20:9-40
55        <activity
55-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:22:9-35:20
56            android:name="com.aios.app.ordering.MainActivity"
56-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:24:13-41
57            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
57-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:23:13-140
58            android:exported="true"
58-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:28:13-36
59            android:label="@string/title_activity_main"
59-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:25:13-56
60            android:launchMode="singleTask"
60-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:27:13-44
61            android:theme="@style/AppTheme.NoActionBarLaunch" >
61-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:26:13-62
62            <intent-filter>
62-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:30:13-33:29
63                <action android:name="android.intent.action.MAIN" />
63-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:31:17-69
63-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:31:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:32:17-77
65-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:32:27-74
66            </intent-filter>
67        </activity>
68
69        <provider
70            android:name="androidx.core.content.FileProvider"
70-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:38:13-62
71            android:authorities="com.aios.app.ordering.fileprovider"
71-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:39:13-64
72            android:exported="false"
72-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:40:13-37
73            android:grantUriPermissions="true" >
73-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:41:13-47
74            <meta-data
74-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:42:13-44:64
75                android:name="android.support.FILE_PROVIDER_PATHS"
75-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:43:17-67
76                android:resource="@xml/file_paths" />
76-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:44:17-51
77        </provider>
78        <provider
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
79            android:name="androidx.startup.InitializationProvider"
79-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
80            android:authorities="com.aios.app.ordering.androidx-startup"
80-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
81            android:exported="false" >
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
82            <meta-data
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.emoji2.text.EmojiCompatInitializer"
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
84                android:value="androidx.startup" />
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
85            <meta-data
85-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
86                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
86-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
87                android:value="androidx.startup" />
87-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
90                android:value="androidx.startup" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
91        </provider>
92
93        <receiver
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
94            android:name="androidx.profileinstaller.ProfileInstallReceiver"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
95            android:directBootAware="false"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
96            android:enabled="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
97            android:exported="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
98            android:permission="android.permission.DUMP" >
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
100                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
103                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
106                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
109                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
110            </intent-filter>
111        </receiver>
112    </application>
113
114</manifest>
