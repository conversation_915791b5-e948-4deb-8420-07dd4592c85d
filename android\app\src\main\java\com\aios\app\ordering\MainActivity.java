package com.aios.app.ordering;

import android.os.Bundle;
import android.webkit.WebSettings;
import android.webkit.JavascriptInterface;
import android.view.Window;
import android.view.WindowManager;
import android.os.Build;
import android.content.res.Resources;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import com.getcapacitor.BridgeActivity;
import com.getcapacitor.community.speechrecognition.SpeechRecognition;

public class MainActivity extends BridgeActivity {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 配置狀態列樣式
        configureStatusBar();

        // 手動註冊 SpeechRecognition 插件
        registerPlugin(SpeechRecognition.class);

        // 啟用 WebView 的語音合成功能
        if (bridge != null && bridge.getWebView() != null) {
            WebSettings webSettings = bridge.getWebView().getSettings();
            webSettings.setJavaScriptEnabled(true);
            webSettings.setDomStorageEnabled(true);
            webSettings.setMediaPlaybackRequiresUserGesture(false);

            // 嘗試啟用語音合成相關功能
            webSettings.setAllowContentAccess(true);
            webSettings.setAllowFileAccess(true);
            
            // 添加JavaScript接口來獲取狀態列高度
            bridge.getWebView().addJavascriptInterface(new StatusBarInterface(), "AndroidStatusBar");
            
            // 配置WebView以支持全屏顯示
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            }
        }
    }

    private void configureStatusBar() {
        Window window = getWindow();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 設定深色狀態列圖標
            window.getDecorView().setSystemUiVisibility(
                android.view.View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            );
        }
        
        // 設定狀態列為透明並允許內容延伸到狀態列下方
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.setStatusBarColor(android.graphics.Color.TRANSPARENT);
            window.setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            );
        }
    }
    
    // JavaScript接口類，用於向網頁提供狀態列高度
    public class StatusBarInterface {
        @JavascriptInterface
        public int getStatusBarHeight() {
            int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (resourceId > 0) {
                return (int) (getResources().getDimensionPixelSize(resourceId) / getResources().getDisplayMetrics().density);
            }
            return 24; // 默認值
        }
        
        @JavascriptInterface
        public boolean hasNotch() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                return getWindow().getDecorView().getRootWindowInsets().getDisplayCutout() != null;
            }
            return false;
        }
    }
}
