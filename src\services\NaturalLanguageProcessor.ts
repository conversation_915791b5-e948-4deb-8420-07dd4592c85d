/**
 * NaturalLanguageProcessor.ts
 * 自然語言處理服務
 * 職責：處理自然語言訂單理解和處理
 */

import { MenuData } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import { GeminiApiClient } from './GeminiApiClient.js';

/**
 * 自然語言處理器
 * 負責將自然語言輸入轉換為結構化的訂單數據
 */
export class NaturalLanguageProcessor {
  private logger = createLogger('NaturalLanguageProcessor');
  private geminiClient: GeminiApiClient;

  constructor(geminiClient?: GeminiApiClient) {
    this.geminiClient = geminiClient || new GeminiApiClient();
    this.logger.info('NaturalLanguageProcessor 已初始化');
  }

  /**
   * 處理自然語言訂單
   * @param input 自然語言輸入
   * @param menuData 菜單數據
   * @param appPrompt 應用提示詞
   * @param sessionId 會話 ID
   * @param language 語言設定
   * @returns 處理後的訂單 JSON 字符串
   */
  async processNaturalLanguageOrder(
    input: string,
    menuData?: MenuData | null,
    appPrompt?: string,
    sessionId?: string,
    language?: string,
    context?: any
  ): Promise<string> {
    this.logger.info('開始處理自然語言訂單', {
      inputLength: input.length,
      hasMenuData: !!menuData,
      hasAppPrompt: !!appPrompt,
      sessionId,
      language: language || 'default'
    });

    try {
      const prompt = this.buildOrderProcessingPrompt(input, menuData, appPrompt, language, context);
      
      this.logger.debug('生成訂單處理提示詞', {
        promptLength: prompt.length
      });

      const response = await this.geminiClient.generateText(prompt);

      this.logger.debug('收到 Gemini 完整回應', {
        responseLength: response.length,
        responsePreview: response.substring(0, 200) + '...'
      });

      // 驗證響應中是否包含有效的 JSON，但返回完整回應
      this.validateResponseContainsValidJson(response);

      this.logger.info('自然語言訂單處理成功', {
        inputLength: input.length,
        responseLength: response.length
      });

      return response; // 返回完整回應，包含自然語言和 JSON
    } catch (error) {
      this.logger.error('自然語言訂單處理失敗', error instanceof Error ? error : new Error(String(error)), {
        inputLength: input.length,
        sessionId
      });
      throw error;
    }
  }

  /**
   * 構建訂單處理提示詞
   */
  private buildOrderProcessingPrompt(
    input: string,
    menuData?: MenuData | null,
    appPrompt?: string,
    language?: string,
    context?: any
  ): string {
    const lang = this.getLanguageSettings(language);
    
    let prompt = '';

    // 添加系統角色（多語言）
    const systemRole = this.getSystemRolePrompt(language);
    prompt += `${systemRole}\\n\\n`;

    // 添加 appPrompt（如果提供）
    if (appPrompt) {
      const systemInstructionLabel = this.getSystemInstructionLabel(language);
      prompt += `${systemInstructionLabel}：\\n${appPrompt}\\n\\n`;
    }

    // 添加菜單信息
    if (menuData) {
      const menuLabel = this.getMenuLabel(language);
      prompt += `${menuLabel}：\\n${JSON.stringify(menuData, null, 2)}\\n\\n`;
    }

    // 添加當前訂單上下文（如果是繼續點餐模式）
    if (context && context.isModifying && context.currentOrder && context.currentOrder.length > 0) {
      const contextInstructions = this.getCurrentOrderContextInstructions(language, context.currentOrder);
      prompt += `${contextInstructions}\\n\\n`;
    }

    // 添加語言設定和餐點名稱要求（多語言）
    const nameField = (language === 'ja-JP' || language === 'ja') ? 'name_jp' : (language === 'en' ? 'name_en' : 'name_zh');
    const languageInstructions = this.getLanguageInstructions(language, lang.displayName, nameField);
    prompt += `${languageInstructions}\n\n`;    // 添加輸出格式要求（多語言）
    const formatInstructions = this.getFormatInstructions(language, nameField, lang.code);
    prompt += `${formatInstructions}\\n\\n`;

    // 添加用戶輸入和處理指示（多語言）
    const userInputAndInstructions = this.getUserInputAndInstructions(language, input);
    prompt += `${userInputAndInstructions}`;

    return prompt;
  }
  /**
   * 驗證響應中是否包含有效的 JSON（但不清理，保留完整回應）
   */
  private validateResponseContainsValidJson(response: string): void {
    try {
      // 首先嘗試提取 ORDER_JSON_START 和 ORDER_JSON_END 之間的內容
      const startMarker = 'ORDER_JSON_START';
      const endMarker = 'ORDER_JSON_END';

      let jsonContent = '';

      const startIndex = response.indexOf(startMarker);
      const endIndex = response.indexOf(endMarker);

      if (startIndex !== -1 && endIndex !== -1) {
        // 提取標記之間的內容
        const jsonStart = startIndex + startMarker.length;
        jsonContent = response.substring(jsonStart, endIndex).trim();
        // 移除可能的 markdown 標記
        jsonContent = jsonContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      } else {
        throw new Error('響應中未找到 ORDER_JSON_START 和 ORDER_JSON_END 標記');
      }

      // 添加調試信息
      this.logger.debug('驗證 JSON 內容', {
        hasMarkers: startIndex !== -1 && endIndex !== -1,
        jsonContentLength: jsonContent.length,
        jsonPreview: jsonContent.substring(0, 100) + '...'
      });

      // 嘗試解析 JSON 以驗證格式
      const parsed = JSON.parse(jsonContent);
      
      // 基本驗證
      if (!parsed.items || !Array.isArray(parsed.items)) {
        throw new Error('響應格式不正確：缺少 items 陣列');
      }      // 驗證每個項目
      for (const item of parsed.items) {
        // 檢查是否有任何有效的名稱欄位
        const hasValidName = !!(item.name || item.name_en || item.name_jp || item.name_zh);
        
        this.logger.debug('驗證項目', {
          item: item,
          hasName: !!item.name,
          hasNameEn: !!item.name_en,
          hasNameJp: !!item.name_jp,
          hasNameZh: !!item.name_zh,
          hasValidName: hasValidName,
          quantityType: typeof item.quantity,
          priceType: typeof item.price
        });
          if (!hasValidName || typeof item.quantity !== 'number' || (item.price !== null && typeof item.price !== 'number')) {
          throw new Error('響應格式不正確：項目格式無效');
        }
      }

      this.logger.debug('響應驗證通過', {
        itemCount: parsed.items.length,
        totalAmount: parsed.totalAmount
      });

      // 驗證成功，不需要返回任何值
    } catch (error) {
      this.logger.error('響應驗證失敗', error instanceof Error ? error : new Error(String(error)), {
        responseLength: response.length
      });

      // 拋出錯誤，讓調用者處理
      throw new Error(`響應驗證失敗: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 獲取語言設定
   */
  private getLanguageSettings(language?: string): { code: string; displayName: string } {
    switch (language) {
      case 'en':
        return { code: 'en', displayName: 'English' };
      case 'ja-JP':
      case 'ja':
        return { code: 'ja-JP', displayName: '日本語' };
      case 'zh':
      case 'tw':
      default:
        return { code: 'zh', displayName: '繁體中文' };
    }
  }

  /**
   * 獲取系統指令標籤（多語言）
   */
  private getSystemInstructionLabel(language?: string): string {
    switch (language) {
      case 'en':
      case 'en-US':
        return 'System Instructions';
      case 'ja-JP':
      case 'ja':
        return 'システム指示';
      default:
        return '系統指令';
    }
  }

  /**
   * 獲取菜單標籤（多語言）
   */
  private getMenuLabel(language?: string): string {
    switch (language) {
      case 'en':
      case 'en-US':
        return 'Available Menu';
      case 'ja-JP':
      case 'ja':
        return '利用可能なメニュー';
      default:
        return '可用菜單';
    }
  }

  /**
   * 獲取系統角色提示詞（多語言）
   */
  private getSystemRolePrompt(language?: string): string {
    switch (language) {
      case 'en':
      case 'en-US':
        return 'You are a professional ordering assistant. You MUST respond in English only. Generate corresponding order data based on the user\'s natural language input. All explanations, notes, and natural language responses must be in English. IMPORTANT: Always use "NT$" for all currency references, never use ¥ or $ symbols.';
      case 'ja-JP':
      case 'ja':
        return 'あなたは専門的な注文アシスタントです。日本語のみで応答してください。ユーザーの自然言語入力に基づいて、対応する注文データを生成してください。すべての説明、注記、自然言語応答は日本語で記述してください。重要：すべての通貨表記には「NT$」を使用し、¥や$記号は絶対に使用しないでください。';
      default:
        return '你是一個專業的點餐助手。請根據用戶的自然語言輸入，生成對應的訂單數據。所有說明、備註和自然語言回應都必須使用繁體中文。重要：所有貨幣表記必須使用「NT$」，絕對不要使用 ¥ 或 $ 符號。';
    }
  }

  /**
   * 獲取語言指示（多語言）
   */
  private getLanguageInstructions(language?: string, displayName?: string, nameField?: string): string {
    switch (language) {
      case 'en':
      case 'en-US':
        return `🌍 CRITICAL LANGUAGE REQUIREMENT: You MUST respond in ${displayName} ONLY. All natural language responses, explanations, and notes must be in English. Do NOT use Chinese or any other language in your response.
Menu Item Name Requirement: Please use the "${nameField}" field from the menu as the item name`;
      case 'ja-JP':
      case 'ja':
        return `🌍 重要な言語要件：${displayName}のみで応答してください。すべての自然言語応答、説明、注記は日本語で記述してください。中国語や他の言語を使用しないでください。
メニュー項目名要件：メニューの「${nameField}」フィールドを項目名として使用してください`;
      default:
        return `🌍 重要語言要求：請使用${displayName}回應。所有自然語言回應、說明和備註都必須使用繁體中文。
餐點名稱要求：請使用菜單中的 "${nameField}" 欄位作為餐點名稱`;
    }
  }

  /**
   * 獲取格式指示（多語言）
   */
  private getFormatInstructions(language?: string, nameField?: string, langCode?: string): string {
    switch (language) {
      case 'en':
      case 'en-US':
        return `Output Format Requirements:
Please return valid JSON format with the following fields:
{
  "items": [
    {
      "${nameField}": "Item Name",
      "quantity": quantity,
      "price": price,
      "image_url": "Image URL"
    }
  ],
  "totalAmount": total_amount,
  "language": "${langCode}",
  "confidence": confidence(0-1),
  "notes": "Additional notes"
}

🔥 Important: Currency and Price Requirements:
- ALL prices must be in New Taiwan Dollar (NT$)
- When mentioning prices in natural language responses, ALWAYS use "NT$" prefix
- Never use ¥, $, or any other currency symbols
- Example: "NT$81" not "¥81" or "$81"

🔥 Important: image_url field requirements:
- Must copy the complete URL from the "image_url" or "image" field in the menu data
- If there is no image URL in the menu, use an empty string ""
- Ensure the URL is complete and accessible`;
      case 'ja-JP':
      case 'ja':
        return `出力フォーマット要件：
以下のフィールドを含む有効なJSON形式で返してください：
{
  "items": [
    {
      "${nameField}": "商品名",
      "quantity": 数量,
      "price": 価格,
      "image_url": "画像URL"
    }
  ],
  "totalAmount": 合計金額,
  "language": "${langCode}",
  "confidence": 信頼度(0-1),
  "notes": "追加備考"
}

🔥 重要：通貨と価格の要件：
- すべての価格は新台湾ドル（NT$）で表示する必要があります
- 自然言語応答で価格を言及する際は、必ず「NT$」プレフィックスを使用してください
- ¥、$、その他の通貨記号は使用しないでください
- 例：「NT$81」であり、「¥81」や「$81」ではありません

🔥 重要：image_urlフィールド要件：
- メニューデータの「image_url」または「image」フィールドから完全なURLをコピーする必要があります
- メニューに画像URLがない場合は、空文字列「""」を使用してください
- URLが完全でアクセス可能であることを確認してください`;
      default:
        return `輸出格式要求：
請返回有效的 JSON 格式，包含以下字段：
{
  "items": [
    {
      "${nameField}": "商品名稱",
      "quantity": 數量,
      "price": 價格,
      "image_url": "圖片網址"
    }
  ],
  "totalAmount": 總金額,
  "language": "${langCode}",
  "confidence": 信心度(0-1),
  "notes": "額外備註"
}

🔥 重要：幣別和價格要求：
- 所有價格必須使用新台幣（NT$）
- 在自然語言回應中提及價格時，必須使用「NT$」前綴
- 絕對不要使用 ¥、$、或其他貨幣符號
- 範例：「NT$81」而不是「¥81」或「$81」

🔥 重要：image_url 字段要求：
- 必須從菜單數據中的 "image_url" 或 "image" 欄位複製完整的 URL
- 如果菜單中沒有圖片 URL，則使用空字符串 ""
- 確保 URL 完整且可訪問`;
    }
  }

  /**
   * 獲取用戶輸入和處理指示（多語言）
   */
  private getUserInputAndInstructions(language?: string, input?: string): string {
    switch (language) {
      case 'en':
      case 'en-US':
        return `User Input: ${input}

🔥 IMPORTANT: Your entire response must be in English. This includes:
- All natural language explanations
- All notes and comments
- All user-facing text
- Do NOT mix languages or use Chinese characters

💰 CRITICAL CURRENCY REQUIREMENT:
- ALL prices must use "NT$" (New Taiwan Dollar) currency symbol
- When mentioning prices in your response, ALWAYS write "NT$" before the amount
- NEVER use ¥, $, or any other currency symbols
- Example: Write "NT$81" not "¥81" or "$81"

Please analyze the user input, identify the required items and quantities, and generate corresponding order data. If there are any uncertainties, please explain them in the notes field IN ENGLISH ONLY.`;
      case 'ja-JP':
      case 'ja':
        return `ユーザー入力：${input}

🔥 重要：応答全体を日本語で記述してください。これには以下が含まれます：
- すべての自然言語説明
- すべての注記とコメント
- すべてのユーザー向けテキスト
- 言語を混在させたり、中国語を使用したりしないでください

💰 重要な通貨要件：
- すべての価格は「NT$」（新台湾ドル）通貨記号を使用する必要があります
- 応答で価格を言及する際は、必ず金額の前に「NT$」を記述してください
- ¥、$、その他の通貨記号は絶対に使用しないでください
- 例：「NT$81」と記述し、「¥81」や「$81」ではありません

ユーザー入力を分析し、必要な商品と数量を特定し、対応する注文データを生成してください。不確実な点がある場合は、notesフィールドで日本語のみで説明してください。`;
      default:
        return `用戶輸入：${input}

🔥 重要：您的整個回應必須使用繁體中文。這包括：
- 所有自然語言說明
- 所有備註和評論
- 所有面向用戶的文字
- 不要混合語言或使用其他語言

💰 重要幣別要求：
- 所有價格必須使用「NT$」（新台幣）貨幣符號
- 在回應中提及價格時，必須在金額前寫上「NT$」
- 絕對不要使用 ¥、$、或其他貨幣符號
- 範例：寫「NT$81」而不是「¥81」或「$81」

請分析用戶輸入，識別所需的商品和數量，並生成對應的訂單數據。如果有不確定的地方，請在 notes 欄位中使用繁體中文說明。`;
    }
  }

  /**
   * 獲取當前訂單上下文指示（多語言）
   */
  private getCurrentOrderContextInstructions(language?: string, currentOrder?: any[]): string {
    if (!currentOrder || currentOrder.length === 0) return '';

    let instructions = '';

    switch (language) {
      case 'en':
      case 'en-US':
        instructions += `Current Order Status:\\n`;
        instructions += `This is a "continue ordering" request. The user already has the following items in their cart:\\n`;
        currentOrder.forEach((item: any, index: number) => {
          const itemName = item.name_en || item.name_zh || item.name_jp || item.name || 'Unknown Item';
          instructions += `${index + 1}. ${itemName} x ${item.quantity} - NT$${item.price * item.quantity}\\n`;
        });
        instructions += `\\n🔥 CRITICAL INSTRUCTIONS:\\n`;
        instructions += `- This is continue ordering mode, the user already has the above items in their cart\\n`;
        instructions += `- You only need to process the items the user is ordering THIS TIME\\n`;
        instructions += `- If the user orders items already in cart (like Big Mac), return only the NEW quantity they're ordering (like 1 Big Mac)\\n`;
        instructions += `- If it's a completely new item (like McFlurry), add it normally to the order\\n`;
        instructions += `- ⚠️ KEY: The returned JSON MUST only contain the newly added items, absolutely DO NOT include existing cart items\\n`;
        instructions += `- ⚠️ Example: If cart has 2 Big Macs and user says "one more Big Mac", you return only 1 Big Mac, not 3`;
        break;
      case 'ja-JP':
      case 'ja':
        instructions += `現在の注文状況：\\n`;
        instructions += `これは「注文を続ける」リクエストです。ユーザーは既に以下の商品をカートに入れています：\\n`;
        currentOrder.forEach((item: any, index: number) => {
          const itemName = item.name_jp || item.name_zh || item.name_en || item.name || '不明な商品';
          instructions += `${index + 1}. ${itemName} x ${item.quantity} - NT$${item.price * item.quantity}\\n`;
        });
        instructions += `\\n🔥 重要な指示：\\n`;
        instructions += `- これは注文継続モードで、ユーザーは既に上記の商品をカートに入れています\\n`;
        instructions += `- 今回ユーザーが注文した商品のみを処理してください\\n`;
        instructions += `- カートに既にある商品（ビッグマックなど）をユーザーが注文した場合、今回の新しい数量のみを返してください（1個のビッグマックなど）\\n`;
        instructions += `- 全く新しい商品（マックフルーリーなど）の場合は、通常通り注文に追加してください\\n`;
        instructions += `- ⚠️ 重要：返されるJSONには新しく追加された商品のみを含め、既存のカート商品は絶対に含めないでください\\n`;
        instructions += `- ⚠️ 例：カートに2個のビッグマックがあり、ユーザーが「ビッグマックをもう1個」と言った場合、1個のビッグマックのみを返し、3個ではありません`;
        break;
      default:
        instructions += `當前訂單狀態：\\n`;
        instructions += `這是一個「繼續點餐」請求，用戶已經有以下餐點在購物車中：\\n`;
        currentOrder.forEach((item: any, index: number) => {
          const itemName = item.name_zh || item.name_en || item.name_jp || item.name || '未知餐點';
          instructions += `${index + 1}. ${itemName} x ${item.quantity} - NT$${item.price * item.quantity}\\n`;
        });
        instructions += `\\n🔥 重要指示：\\n`;
        instructions += `- 這是繼續點餐模式，用戶已有上述餐點在購物車中\\n`;
        instructions += `- 您只需要處理用戶這次新點的餐點\\n`;
        instructions += `- 如果用戶點的餐點已經在購物車中（如大麥克），請返回用戶這次新點的數量（如1份大麥克）\\n`;
        instructions += `- 如果是全新的餐點（如冰炫風），請正常添加到訂單中\\n`;
        instructions += `- ⚠️ 關鍵：返回的 JSON 必須只包含這次新增的餐點，絕對不要包含購物車中已存在的餐點\\n`;
        instructions += `- ⚠️ 例如：如果購物車有2份大麥克，用戶說"再來一份大麥克"，您只能返回1份大麥克，不是3份`;
        break;
    }

    return instructions;
  }

  /**
   * 批量處理多個自然語言輸入
   * @param inputs 輸入陣列
   * @param menuData 菜單數據
   * @param appPrompt 應用提示詞
   * @param sessionId 會話 ID
   * @param language 語言設定
   * @returns 處理結果陣列
   */
  async processBatchNaturalLanguageOrders(
    inputs: string[], 
    menuData?: MenuData | null, 
    appPrompt?: string, 
    sessionId?: string, 
    language?: string
  ): Promise<string[]> {
    this.logger.info('開始批量處理自然語言訂單', {
      batchSize: inputs.length,
      sessionId
    });

    const results: string[] = [];
    
    for (let i = 0; i < inputs.length; i++) {
      try {
        const result = await this.processNaturalLanguageOrder(
          inputs[i], 
          menuData, 
          appPrompt, 
          `${sessionId}-batch-${i}`, 
          language
        );
        results.push(result);
      } catch (error) {
        this.logger.error(`批量處理第 ${i + 1} 項失敗`, error instanceof Error ? error : new Error(String(error)));
        
        // 添加錯誤響應
        results.push(JSON.stringify({
          items: [],
          totalAmount: 0,
          language: language || 'zh',
          confidence: 0,
          notes: `處理失敗: ${error instanceof Error ? error.message : String(error)}`
        }, null, 2));
      }
    }

    this.logger.info('批量處理完成', {
      batchSize: inputs.length,
      successCount: results.length
    });

    return results;
  }
}

export default new NaturalLanguageProcessor();
