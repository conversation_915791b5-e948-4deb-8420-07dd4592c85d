# 🍽️ Smart Ordering - 智能點餐 Android 應用

一個基於 Capacitor 的跨平台智能點餐 Android 應用程式，整合了自然語言處理、語音識別和 AI 助手功能，讓用戶可以透過自然語言（文字或語音）進行餐點訂購。

## 📱 應用特色

- **🤖 AI 智能點餐**：整合 Google Gemini AI，支援自然語言對話式點餐
- **🎤 語音識別**：支援中文語音輸入，即時語音轉文字
- **🔊 語音合成**：基於 VITS 神經網路的高品質離線 TTS，支援多語言語音播報
- **🌍 多語言支援**：繁體中文、英文、日文完整本地化
- **📱 原生體驗**：基於 Capacitor 框架，提供接近原生的使用體驗
- **☁️ 雲端整合**：連接遠程服務器，支援即時數據同步

## 🛠️ 技術架構

### 前端技術
- **Capacitor 7.3.0**：跨平台應用框架
- **HTML5/CSS3/JavaScript**：現代化 Web 技術
- **Web Speech API**：語音識別與合成
- **響應式設計**：適配各種螢幕尺寸

### 後端服務
- **Node.js + Express**：RESTful API 服務
- **TypeScript**：類型安全的開發體驗
- **Google Gemini AI**：自然語言處理
- **Firebase/Firestore**：雲端數據庫

### 語音功能
- **@capacitor-community/speech-recognition**：語音識別插件
- **@capacitor-community/text-to-speech**：語音合成插件
- **@diffusionstudio/vits-web**：VITS 神經網路離線 TTS 引擎
- **智能語音選擇**：自動選擇最佳中文語音引擎
- **降噪處理**：提高語音識別準確率
- **多層回退機制**：確保語音輸出在各種設備上都能工作
- **TTS 引擎檢測**：自動檢測並引導 TTS 引擎安裝
- **跨平台兼容**：支援有/無 Google Services 的 Android 設備
- **離線語音合成**：基於神經網路的高品質離線 TTS 技術

### 其他插件
- **@capacitor/camera**：相機功能
- **@capacitor/filesystem**：文件系統存取
- **@capacitor/splash-screen**：啟動畫面
- **@capacitor/status-bar**：狀態列控制

## 🚀 快速開始

### 環境需求
- **Node.js** 18+
- **Android Studio** (用於 Android 開發)
- **Capacitor CLI** 7.3.0+
- **Google Gemini API** 金鑰
- **Firebase** 專案配置

### 安裝與設置

1. **克隆專案**：
   ```bash
   git clone <repository-url>
   cd "New Natural Order - Android"
   ```

2. **安裝依賴**：
   ```bash
   npm install
   ```

3. **環境配置**：
   ```bash
   cp .env.example .env.android
   # 編輯 .env.android 文件，填入您的配置
   ```

4. **編譯 TypeScript**：
   ```bash
   npm run build
   ```

5. **同步 Capacitor**：
   ```bash
   npx cap sync android
   ```

6. **在 Android Studio 中開啟**：
   ```bash
   npx cap open android
   ```

### 開發模式

```bash
# 啟動開發服務器
npm run dev

# 同步到 Android
npx cap sync android

# 在設備上運行
npx cap run android
```

## 📋 主要功能

### 🎤 語音點餐
- **語音識別**：點擊麥克風按鈕開始語音輸入
- **即時轉換**：語音即時轉換為文字
- **智能理解**：AI 理解複雜的點餐需求
- **語音確認**：訂單確認語音播報

### 💬 文字點餐
- **自然語言輸入**：支援自然的中文表達
- **智能解析**：自動識別餐點、數量、特殊要求
- **即時回應**：AI 助手即時回應和建議
- **訂單確認**：詳細的訂單摘要顯示

### 📱 用戶界面
- **直觀設計**：簡潔易用的操作界面
- **多語言切換**：支援繁中/英文/日文切換
- **響應式布局**：適配不同螢幕尺寸
- **無障礙支援**：支援語音操作和視覺輔助

### ☁️ 雲端服務
- **即時同步**：訂單數據即時同步到雲端
- **菜單管理**：動態載入最新菜單信息
- **用戶歷史**：保存用戶點餐歷史記錄
- **多設備同步**：支援多設備數據同步

## 🔧 配置說明

### Capacitor 配置

```typescript
// capacitor.config.ts
const config: CapacitorConfig = {
  appId: 'com.aios.app.ordering',
  appName: 'Smart Ordering',
  webDir: 'public',
  server: {
    url: 'https://rudylee.eu.org:4003' // 遠程服務器
  }
};
```

### 環境變數

```bash
# .env.android
GEMINI_API_KEY=your_gemini_api_key
FIREBASE_CONFIG=your_firebase_config
USE_GEMINI_AI=true
GEMINI_MODEL=gemini-1.5-pro
LOG_LEVEL=INFO
```

## 🎯 語音功能詳解

### 語音識別 (Speech Recognition)
- **多語言支援**：支援中文、英文、日文語音識別
- **連續識別**：支援長時間連續語音輸入
- **即時結果**：提供即時和最終識別結果
- **錯誤處理**：智能處理網路錯誤和權限問題
- **修復狀態管理**：解決語音識別 "aborted" 錯誤問題
- **改進用戶體驗**：優化語音識別流程和反饋機制

### 語音合成 (Text-to-Speech)
- **智能語音選擇**：自動選擇最佳中文語音引擎
- **語音參數優化**：調整語速、音調、音量
- **多語言播報**：支援中英日三語語音播報
- **多層回退機制**：Capacitor TTS → 瀏覽器 TTS → 文字通知
- **TTS 引擎檢測**：自動檢測並引導用戶安裝 TTS 引擎
- **無 Google Services 支援**：針對沒有 Google Services 的設備提供替代方案

### 🎵 離線 TTS 技術 (VITS Neural Network)
- **@diffusionstudio/vits-web**：基於 VITS 神經網路的高品質離線 TTS
- **完全離線運作**：無需網路連接，保護用戶隱私
- **神經網路語音合成**：使用先進的 VITS (Variational Inference with adversarial learning for end-to-end Text-to-Speech) 技術
- **高品質語音輸出**：接近真人語音的自然度和清晰度
- **多語言支援**：支援中文、英文、日文的離線語音合成
- **輕量化部署**：優化的模型大小，適合移動設備運行
- **即時語音生成**：快速的語音合成響應時間
- **跨平台兼容**：支援 Web 和 Android 平台

### 語音優化特性
- **動態語音檢測**：檢測設備可用語音包
- **優先級選擇**：台灣中文 > 香港中文 > 其他中文語音
- **參數調優**：語速 0.9、音量 0.9，確保清晰度
- **詳細日誌**：完整的語音操作日誌記錄
- **多語言嘗試**：依序嘗試 zh-TW、zh-CN、zh、en-US 語言設定
- **WebView 優化**：針對 Android WebView 環境的特殊處理

### 🔧 最新語音修復 (2025-06-25)

#### 語音識別修復
- **✅ 修復 "aborted" 錯誤**：解決語音識別中斷問題
- **✅ 改進狀態管理**：優化語音識別狀態追蹤
- **✅ 增強錯誤處理**：更好的錯誤恢復機制
- **✅ 用戶體驗優化**：清晰的視覺和聲音反饋

#### 語音合成修復
- **✅ 多層回退系統**：
  1. 優先使用 Capacitor TTS
  2. 失敗時回退到瀏覽器 TTS
  3. 最終回退到文字通知 + 震動
- **✅ TTS 引擎檢測**：自動檢測設備 TTS 引擎可用性
- **✅ 安裝指引**：提供詳細的 TTS 引擎安裝指導
- **✅ 多語言嘗試**：自動嘗試多種語言代碼
- **✅ 無 Google Services 支援**：針對特殊設備的解決方案

#### Android 平台優化
- **✅ WebView 配置**：啟用語音合成相關功能
- **✅ 權限配置**：添加 TTS 服務查詢權限
- **✅ 插件同步**：確保所有 Capacitor 插件正確安裝

### 📱 Android WebView 狀態栏適配修復 (2025-06-27)

#### 問題描述
在 Android WebView 環境中，系統狀態栏會遮擋應用內容，導致頂部元素（如標題欄、按鈕）被狀態栏覆蓋，影響用戶體驗。

#### 解決方案
實現了一套完整的狀態栏動態適配系統，包含 Android 原生端和 Web 端的協同處理：

##### Android 原生端修改 (`MainActivity.java`)
- **✅ 新增 StatusBarInterface 類**：提供狀態栏高度檢測和劉海屏檢測功能
- **✅ JavaScript 接口暴露**：通過 `AndroidStatusBar` 接口向 Web 端提供狀態栏信息
- **✅ 狀態栏配置優化**：啟用內容延伸到狀態栏下方，實現沉浸式體驗
- **✅ 動態高度計算**：實時獲取狀態栏實際高度，適配不同設備
- **✅ 劉海屏檢測**：檢測設備是否有劉海屏或挖孔屏

```java
// 主要功能
public class StatusBarInterface {
    public int getStatusBarHeight()    // 獲取狀態栏高度
    public boolean hasNotch()          // 檢測劉海屏
}

// JavaScript 接口
webView.addJavascriptInterface(new StatusBarInterface(this), "AndroidStatusBar");
```

##### Web 端動態適配 (`index.html`)
- **✅ 智能狀態栏處理**：優先使用 Android 提供的實際狀態栏高度
- **✅ 動態樣式調整**：自動調整 `body` 的 `padding-top`、`header` 的 `top` 和 `container` 的 `margin-top`
- **✅ 多環境兼容**：支援 Android WebView 和 Capacitor 環境
- **✅ 降級處理**：當 Android 接口不可用時，自動回退到默認處理
- **✅ 詳細日誌**：完整的狀態栏配置日誌，便於調試

```javascript
// 核心功能
function configureStatusBar() {
    // 1. 優先使用 Android 提供的狀態栏高度
    // 2. 動態設置 CSS 樣式
    // 3. 兼容 Capacitor 環境
    // 4. 提供降級處理
}
```

##### 技術特點
- **🎯 精確適配**：使用設備實際狀態栏高度，而非固定值
- **🔄 動態響應**：支援設備旋轉和狀態栏變化
- **🛡️ 兼容性強**：同時支援有/無 Google Services 的設備
- **📱 多設備支援**：適配不同品牌和型號的 Android 設備
- **🔧 易於維護**：清晰的代碼結構和詳細的註釋
- **📊 調試友好**：豐富的日誌輸出，便於問題排查

##### 修改文件清單
- `android/app/src/main/java/com/aios/app/ordering/MainActivity.java` - Android 原生狀態栏接口
- `public/index.html` - Web 端動態適配邏輯
- 移除所有固定的 CSS 狀態栏設置，改為 JavaScript 動態控制

##### 使用效果
- ✅ 狀態栏不再遮擋應用內容
- ✅ 支援各種 Android 設備和螢幕尺寸
- ✅ 自動適配劉海屏和挖孔屏設備
- ✅ 提供流暢的沉浸式用戶體驗
- ✅ 保持與 Capacitor 框架的完全兼容性

## 🧪 測試

### 單元測試
```bash
# 運行所有測試
npm test

# 運行 Jest 測試
npm run test:jest

# 測試覆蓋率
npm run test:coverage

# 監視模式
npm run test:jest:watch
```

### 設備測試
```bash
# 在 Android 設備上測試
npx cap run android

# 在 Android 模擬器上測試
npx cap run android --target=emulator
```

## 📱 部署指南

### Android APK 構建

1. **準備發布版本**：
   ```bash
   npm run build
   npx cap sync android
   ```

2. **在 Android Studio 中**：
   - 開啟 `android` 目錄
   - 選擇 `Build > Generate Signed Bundle / APK`
   - 選擇 APK 或 Bundle 格式
   - 配置簽名金鑰
   - 構建發布版本

3. **命令行構建**：
   ```bash
   cd android
   ./gradlew assembleRelease
   ```

### 發布到 Google Play

1. **準備應用**：
   - 更新版本號 (`android/app/build.gradle`)
   - 準備應用圖標和截圖
   - 撰寫應用描述

2. **上傳到 Play Console**：
   - 創建新版本
   - 上傳 AAB 文件
   - 填寫版本說明
   - 提交審核

## 🔍 故障排除

### 常見問題

**Q: 語音識別無法使用**
A: 請檢查：
- 麥克風權限是否已授予
- 設備是否支援語音識別
- 網路連線是否正常
- 嘗試重新啟動應用

**Q: 語音識別出現 "aborted" 錯誤**
A: 這是已知問題，解決方案：
- 確保使用最新版本的應用
- 檢查麥克風權限設定
- 避免在語音識別過程中切換應用
- 如果問題持續，請重新啟動應用

**Q: 語音合成沒有聲音**
A: 本應用使用多層 TTS 系統，請按以下步驟檢查：

1. **基本檢查**：
   - 設備音量是否開啟
   - 媒體音量是否調整到適當大小
   - 其他應用的語音播放是否正常

2. **VITS 離線 TTS 檢查**：
   - 檢查瀏覽器是否支援 Web Audio API
   - 確認網路連接正常（首次載入 VITS 模型需要）
   - 查看控制台是否有 VITS 載入錯誤信息

3. **系統 TTS 引擎檢查**：
   - 設定 → 協助工具 → 文字轉語音輸出
   - 檢查是否已安裝 TTS 引擎
   - 嘗試測試語音輸出功能

4. **安裝 TTS 引擎**：
   - **有 Google Play 商店**：安裝 "Google 文字轉語音"
   - **無 Google Services**：尋找第三方 TTS 應用 (APK)
   - 設定為預設 TTS 引擎

5. **應用內檢查**：
   - 查看應用日誌中的語音設定信息
   - 檢查是否顯示 TTS 安裝指引
   - 嘗試重新啟動應用
   - 確認 VITS TTS 模型是否成功載入

**Q: VITS 離線 TTS 無法正常工作**
A: VITS 神經網路 TTS 故障排除：
- **首次載入慢**：VITS 模型較大，首次載入需要時間和穩定網路
- **瀏覽器兼容性**：確保使用支援 Web Audio API 的現代瀏覽器
- **記憶體不足**：VITS 需要足夠記憶體，關閉其他應用釋放記憶體
- **模型載入失敗**：檢查網路連接，清除瀏覽器快取後重試
- **音質問題**：調整設備音量和音質設定
- **回退機制**：如果 VITS 失敗，系統會自動回退到系統 TTS

**Q: Android 設備沒有 Google Services**
A: 針對沒有 Google Services 的設備：
- **VITS 離線 TTS 仍可正常使用**：完全不依賴 Google Services
- 應用會自動切換到文字通知模式作為最終回退
- 可以尋找第三方 TTS 引擎 APK 文件
- 或使用純文字模式進行點餐
- 語音識別功能不受影響

**Q: 購物車記憶功能無法正常工作**
A: 購物車記憶功能故障排除：

1. **「繼續點餐」按鈕無效**：
   - 確保使用最新版本 (v1.0.3+)
   - 檢查瀏覽器是否支援 sessionStorage
   - 嘗試清除瀏覽器快取後重新載入
   - 查看控制台是否有 JavaScript 錯誤

2. **餐點數量不正確**：
   - 檢查是否有重複的餐點項目
   - 確認餐點名稱和價格匹配邏輯
   - 查看控制台中的餐點匹配日誌
   - 嘗試重新添加餐點到購物車

3. **Android 端購物車記憶失效**：
   - 確保 Android APK 是使用最新代碼編譯
   - 檢查 `android/app/src/main/assets/public/index.html` 是否已更新
   - 執行 `npx cap sync android` 重新同步資源
   - 重新編譯和安裝 Android APK

4. **調試購物車問題**：
   - 開啟瀏覽器開發者工具
   - 查看 Console 標籤中的購物車日誌
   - 檢查 Application → Storage → Session Storage
   - 確認 `currentOrderItems` 鍵是否存在且有正確數據

**Q: 無法連接到服務器**
A: 請檢查：
- 網路連線是否正常
- 服務器 URL 是否正確
- 防火牆設定是否阻擋連線
- 嘗試切換網路環境

**Q: AI 無法回應**
A: 請檢查：
- Gemini API 金鑰是否正確
- API 配額是否已用完
- 服務器是否正常運行
- 查看網路請求日誌

### 調試技巧

```bash
# 查看設備日誌
adb logcat | grep -i "smart ordering"

# 查看 Capacitor 日誌
npx cap run android --livereload

# 檢查網路請求
# 在 Chrome DevTools 中查看 Network 標籤
```

## 🔄 版本歷史

### v1.0.3 (2025-07-02) - 購物車記憶功能修復
- 🛒 **購物車記憶功能重大修復**：
  - ✅ 修復 sessionStorage 鍵名不一致問題 (currentOrderObjects → currentOrderItems)
  - ✅ 改進餐點匹配邏輯，使用更嚴格的名稱和價格檢查
  - ✅ 增強調試日誌，追蹤購物車狀態變化
  - ✅ 統一「繼續點餐」功能的數據保存和恢復機制
  - ✅ 解決餐點數量累加和新增邏輯問題

- 📱 **Android 端同步修復**：
  - ✅ 使用 `npx cap sync android` 正確同步程式碼
  - ✅ 修復 Android assets 目錄中的 sessionStorage 鍵名
  - ✅ 確保 Android APK 編譯時包含最新修復
  - ✅ 驗證 Android 端購物車記憶功能正常運作

- 🔧 **技術改進**：
  - ✅ 移除廢棄的 currentOrderObjects 清理代碼
  - ✅ 優化 Capacitor 資源同步流程
  - ✅ 改進購物車狀態管理的一致性
  - ✅ 增加詳細的購物車操作日誌記錄

### v1.0.2 (2025-06-27) - 語音識別準確度優化
- 🎤 **英文語音識別大幅改進**：
  - ✅ 解決英文語音識別信心度極低問題 (從 0.01 提升到 >0.5)
  - ✅ 優化語言代碼設定，使用更通用的 'en' 而非 'en-US'
  - ✅ 增加候選結果數量從 3 個提升到 5 個
  - ✅ 添加詳細的信心度診斷和建議系統
  - ✅ 實現多語言語音識別準確度一致性

- 🔊 **離線 TTS 技術整合**：
  - ✅ 整合 @diffusionstudio/vits-web VITS 神經網路 TTS
  - ✅ 實現完全離線的高品質語音合成
  - ✅ 支援中英日三語的神經網路語音輸出
  - ✅ 優化語音合成品質和響應速度

- 🌍 **多語言語音優化**：
  - ✅ 修復語言切換時語音識別語言同步問題
  - ✅ 優化語言按鈕動態顯示功能
  - ✅ 改進語音識別執行順序和參數傳遞

### v1.0.1 (2025-06-25) - 語音功能大幅改進
- 🔧 **語音識別修復**：
  - ✅ 修復語音識別 "aborted" 錯誤問題
  - ✅ 改進語音識別狀態管理
  - ✅ 優化錯誤處理和用戶反饋
  - ✅ 增強語音識別穩定性

- 🔊 **語音合成重大改進**：
  - ✅ 實現多層回退機制 (Capacitor TTS → 瀏覽器 TTS → 文字通知)
  - ✅ 添加 TTS 引擎自動檢測功能
  - ✅ 提供詳細的 TTS 安裝指引
  - ✅ 支援多語言代碼嘗試 (zh-TW, zh-CN, zh, en-US)
  - ✅ 針對無 Google Services 設備的特殊支援

- 📱 **Android 平台優化**：
  - ✅ 優化 WebView 語音功能配置
  - ✅ 添加 TTS 服務查詢權限
  - ✅ 改進 Capacitor 插件同步機制

### v1.0.0 (初始版本)
- ✅ 基礎 Android 應用框架
- ✅ 語音識別與合成功能
- ✅ AI 智能點餐助手
- ✅ 多語言界面支援
- ✅ 雲端服務整合
- ✅ 中文語音優化
- ✅ 響應式用戶界面

### 計劃功能
- 🔄 離線模式支援
- 🔄 用戶偏好設定
- 🔄 訂單歷史查詢
- 🔄 推送通知
- 🔄 社交分享功能

## 🤝 貢獻指南

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

### 開發規範
- 遵循 TypeScript 編碼規範
- 所有新功能都需要包含測試
- 保持代碼覆蓋率在 80% 以上
- 使用有意義的 commit 訊息
- 測試 Android 設備兼容性

## 📄 許可證

本專案使用 ISC 許可證 - 詳見 [LICENSE](LICENSE) 文件

## 🙏 致謝

- **Capacitor 團隊** - 提供優秀的跨平台框架
- **Google Generative AI 團隊** - 提供強大的 Gemini AI 服務
- **Firebase 團隊** - 提供可靠的雲端數據庫解決方案
- **Capacitor Community** - 提供語音識別和 TTS 插件
- **Diffusion Studio** - 提供優秀的 VITS Web TTS 解決方案
- **VITS 研究團隊** - 開發先進的神經網路語音合成技術
- **Android 開發社群** - 提供豐富的開發資源和支援
- **所有測試用戶** - 協助改進應用功能和用戶體驗

### 技術支援
- [Capacitor 官方文檔](https://capacitorjs.com/docs)
- [Google Gemini AI 官方文檔](https://ai.google.dev/)
- [Firebase 官方文檔](https://firebase.google.com/docs)
- [Android 開發者文檔](https://developer.android.com/)
- [Capacitor Community 插件](https://github.com/capacitor-community)

---

**Smart Ordering** - 讓點餐變得更智能、更簡單！ 🍽️✨
