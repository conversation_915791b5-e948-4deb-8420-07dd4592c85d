{"name": "natural-order", "version": "1.0.0", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "cross-env NODE_ENV=development PORT=3004 LOG_LEVEL=DEBUG tsx src/index.ts", "dev:prod-port": "cross-env NODE_ENV=production PORT=3005 LOG_LEVEL=INFO tsx src/index.ts", "dev:gui": "cross-env NODE_ENV=development PORT=4003 LOG_LEVEL=DEBUG tsx src/gui-server.ts", "start:gui": "node dist/gui-server.js", "test": "npx tsx src/tests/helpers/test-runner.ts", "test:unit": "npx tsx src/tests/helpers/test-runner.ts --unit", "test:integration": "npx tsx src/tests/helpers/test-runner.ts --integration", "test:e2e": "npx tsx src/tests/helpers/test-runner.ts --e2e", "test:direct": "npx tsx src/tests/helpers/test-runner.ts", "test:jest": "jest", "test:jest:watch": "jest --watch", "test:coverage": "jest --coverage", "test:jest:verbose": "jest --verbose", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@capacitor-community/speech-recognition": "^7.0.1", "@capacitor-community/text-to-speech": "^6.0.0", "@capacitor/android": "^7.3.0", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@capacitor/filesystem": "^7.1.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@diffusionstudio/vits-web": "^1.0.3", "@google/generative-ai": "^0.2.1", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase": "^11.8.1", "form-data": "^4.0.2", "multer": "^2.0.0", "typescript": "^5.8.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/multer": "^1.4.13", "cross-env": "^7.0.3", "jest": "^30.0.0", "nodemon": "^3.0.3", "ts-jest": "^29.4.0", "tsx": "^4.19.4"}}