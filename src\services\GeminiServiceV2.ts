/**
 * GeminiServiceV2.ts
 * 重構後的 Gemini 服務
 * 採用組合模式，整合拆分後的專業服務
 * 職責：作為統一的入口點，協調各個專業服務
 */

import { BDDSpec, AAPrompt, APPPromptResult, MenuData } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import { GeminiApiClient } from './GeminiApiClient.js';
import { PromptGenerationService } from './PromptGenerationService.js';
import { NaturalLanguageProcessor } from './NaturalLanguageProcessor.js';

/**
 * Gemini 服務 V2
 * 重構後的服務，採用組合模式和依賴注入
 */
export class GeminiServiceV2 {
  private logger = createLogger('GeminiServiceV2');
  private geminiClient: GeminiApiClient;
  private promptGenerator: PromptGenerationService;
  private nlProcessor: NaturalLanguageProcessor;

  constructor(
    geminiClient?: GeminiApiClient,
    promptGenerator?: PromptGenerationService,
    nlProcessor?: NaturalLanguageProcessor
  ) {
    // 依賴注入 - 可以注入自定義的服務實例
    this.geminiClient = geminiClient || new GeminiApiClient();
    this.promptGenerator = promptGenerator || new PromptGenerationService(this.geminiClient);
    this.nlProcessor = nlProcessor || new NaturalLanguageProcessor(this.geminiClient);

    this.logger.info('GeminiServiceV2 已初始化', {
      servicesCount: 3,
      architecture: 'modular'
    });
  }

  /**
   * 從 BDD 規範生成 APPPrompt
   * 委託給 PromptGenerationService
   */
  async generateFromBDD(bddSpec: BDDSpec, menuData?: MenuData | null, language?: string): Promise<APPPromptResult> {
    this.logger.info('委託 BDD 規範生成給 PromptGenerationService');
    return this.promptGenerator.generateFromBDD(bddSpec, menuData, language);
  }

  /**
   * 從 AA Prompt 生成 APPPrompt
   * 委託給 PromptGenerationService
   */
  async generateFromAA(aaPrompt: AAPrompt, menuData?: MenuData | null, language?: string): Promise<APPPromptResult> {
    this.logger.info('委託 AA Prompt 生成給 PromptGenerationService');
    return this.promptGenerator.generateFromAA(aaPrompt, menuData, language);
  }

  /**
   * 處理自然語言訂單
   * 委託給 NaturalLanguageProcessor
   */
  async processNaturalLanguageOrder(
    input: string,
    menuData?: MenuData | null,
    appPrompt?: string,
    sessionId?: string,
    language?: string,
    context?: any
  ): Promise<string> {
    this.logger.info('委託自然語言訂單處理給 NaturalLanguageProcessor');
    return this.nlProcessor.processNaturalLanguageOrder(input, menuData, appPrompt, sessionId, language, context);
  }

  /**
   * 從自然語言解析生成 APPPrompt
   * 委託給 PromptGenerationService
   */
  async parseNaturalLanguageToAppPrompt(input: string, menuData?: MenuData | null, sessionId?: string): Promise<APPPromptResult> {
    this.logger.info('委託自然語言解析給 PromptGenerationService');
    return this.promptGenerator.parseNaturalLanguageToAppPrompt(input, menuData, sessionId);
  }

  /**
   * 批量處理自然語言訂單
   * 委託給 NaturalLanguageProcessor
   */
  async processBatchNaturalLanguageOrders(
    inputs: string[], 
    menuData?: MenuData | null, 
    appPrompt?: string, 
    sessionId?: string, 
    language?: string
  ): Promise<string[]> {
    this.logger.info('委託批量訂單處理給 NaturalLanguageProcessor', {
      batchSize: inputs.length
    });
    return this.nlProcessor.processBatchNaturalLanguageOrders(inputs, menuData, appPrompt, sessionId, language);
  }

  /**
   * 健康檢查 - 檢查所有服務的狀態
   */
  async healthCheck(): Promise<{
    overall: boolean;
    services: {
      geminiClient: boolean;
      promptGenerator: boolean;
      nlProcessor: boolean;
    };
  }> {
    this.logger.info('執行 GeminiServiceV2 健康檢查');

    try {
      // 檢查 Gemini API 客戶端
      const geminiClientHealth = await this.geminiClient.healthCheck();

      // 其他服務的健康檢查（簡單檢查）
      const promptGeneratorHealth = !!this.promptGenerator;
      const nlProcessorHealth = !!this.nlProcessor;

      const result = {
        overall: geminiClientHealth && promptGeneratorHealth && nlProcessorHealth,
        services: {
          geminiClient: geminiClientHealth,
          promptGenerator: promptGeneratorHealth,
          nlProcessor: nlProcessorHealth
        }
      };

      this.logger.info('健康檢查完成', result);
      return result;
    } catch (error) {
      this.logger.error('健康檢查失敗', error instanceof Error ? error : new Error(String(error)));
      return {
        overall: false,
        services: {
          geminiClient: false,
          promptGenerator: false,
          nlProcessor: false
        }
      };
    }
  }

  /**
   * 獲取服務統計信息
   */
  getServiceInfo(): {
    version: string;
    architecture: string;
    services: string[];
    features: string[];
  } {
    return {
      version: '2.0.0',
      architecture: 'modular',
      services: [
        'GeminiApiClient',
        'PromptGenerationService', 
        'NaturalLanguageProcessor'
      ],
      features: [
        'BDD to APPPrompt',
        'AA to APPPrompt',
        'Natural Language Processing',
        'Batch Processing',
        'Health Monitoring',
        'Dependency Injection'
      ]
    };
  }

  /**
   * 更新 Gemini API 配置
   */
  updateGeminiConfig(config: any): void {
    this.logger.info('更新 Gemini API 配置');
    this.geminiClient.updateDefaultConfig(config);
  }

  /**
   * 便利方法：直接調用 Gemini API
   * 用於需要直接與 Gemini 交互的場景
   */
  async directGeminiCall(prompt: string, config?: any): Promise<string> {
    this.logger.debug('執行直接 Gemini API 調用', {
      promptLength: prompt.length
    });
    return this.geminiClient.generateText(prompt, config);
  }
}

export default new GeminiServiceV2();
