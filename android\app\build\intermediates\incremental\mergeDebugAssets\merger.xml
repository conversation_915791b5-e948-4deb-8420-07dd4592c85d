<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\capacitor-cordova-android-plugins\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="native-bridge.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out\native-bridge.js"/></source></dataSet><dataSet config=":capacitor-status-bar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\status-bar\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\splash-screen\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-filesystem" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\filesystem\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\camera\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-community-text-to-speech" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-community-speech-recognition" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor-community\speech-recognition\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/cordova.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/css/fallback-speech.css" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\css\fallback-speech.css"/><file name="public/css/language-switcher.css" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\css\language-switcher.css"/><file name="public/css/order.css" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\css\order.css"/><file name="public/css/toast-fix.css" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\css\toast-fix.css"/><file name="public/css/toast.css" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\css\toast.css"/><file name="public/favicon.ico" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\favicon.ico"/><file name="public/index.html" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\index.html"/><file name="public/js/android-speech-detector.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\android-speech-detector.js"/><file name="public/js/checkout-fix.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\checkout-fix.js"/><file name="public/js/checkout-test.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\checkout-test.js"/><file name="public/js/editor.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\editor.js"/><file name="public/js/enhanced-gemini-extraction.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\enhanced-gemini-extraction.js"/><file name="public/js/extraction-test.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\extraction-test.js"/><file name="public/js/fallback-speech-ui.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\fallback-speech-ui.js"/><file name="public/js/fix-upload.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\fix-upload.js"/><file name="public/js/gemini-helper.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\gemini-helper.js"/><file name="public/js/language-resources.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\language-resources.js"/><file name="public/js/menu-display-fixer.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\menu-display-fixer.js"/><file name="public/js/menu-price-validator.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\menu-price-validator.js"/><file name="public/js/order-cleanup.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\order-cleanup.js"/><file name="public/js/order-confirmation-fix.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\order-confirmation-fix.js"/><file name="public/js/order-summary-diagnostics.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\order-summary-diagnostics.js"/><file name="public/js/order.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\order.js"/><file name="public/js/session-manager.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\session-manager.js"/><file name="public/js/speech-manager.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\speech-manager.js"/><file name="public/js/table-data-sync.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\js\table-data-sync.js"/><file name="public/offline-tts-service.js" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\offline-tts-service.js"/><file name="public/ort-wasm-simd-threaded.jsep.mjs" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\ort-wasm-simd-threaded.jsep.mjs"/><file name="public/ort-wasm-simd-threaded.jsep.wasm" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\ort-wasm-simd-threaded.jsep.wasm"/><file name="public/ort-wasm-simd-threaded.mjs" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\ort-wasm-simd-threaded.mjs"/><file name="public/ort-wasm-simd-threaded.wasm" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\ort-wasm-simd-threaded.wasm"/><file name="public/test-speech.html" path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\assets\public\test-speech.html"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>